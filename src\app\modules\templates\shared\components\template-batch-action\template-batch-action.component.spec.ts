import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateBatchActionComponent } from './template-batch-action.component';
import { NO_ERRORS_SCHEMA, InjectionToken } from '@angular/core';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '@appServices/common/auth.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization/service/permissions.service';
import { SearchOfferRequestService } from '@appModules/request/services/search-offer-request.service';
import { ToastrService } from 'ngx-toastr';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';

// Minimal mock class for PermissionsService
class MockPermissionsService {}

// Mock class for FacetItemService
class MockFacetItemService {
  templateProgramCodeSelected = 'PCODE';
  getAppDataName = jasmine.createSpy('getAppDataName').and.returnValue('TestApp');
  getDeliveryChannels = jasmine.createSpy('getDeliveryChannels').and.returnValue([]);
  // Add all FacetItemService methods/properties used by the component as spies/stubs
  getFacetItems = jasmine.createSpy('getFacetItems').and.returnValue({});
  getdivsionStateFacetItems = jasmine.createSpy('getdivsionStateFacetItems').and.returnValue({});
  getOfferFilter = jasmine.createSpy('getOfferFilter').and.returnValue('facetFilter');
  setOfferFilter = jasmine.createSpy('setOfferFilter');
  // Add any other methods/properties as needed for test coverage
}
const mockBulkUpdateService = { requestIdArr: [1, 2, 3] };
const mockPopoverRef = { hide: jasmine.createSpy('hide') };

// Minimal mock class for BsModalService
class MockBsModalService {
  hide = jasmine.createSpy('hide');
  show = jasmine.createSpy('show');
}

// Minimal mocks for additional dependencies
const mockSearchOfferRequestService = {};
const mockAuthService = { getTokenString: () => 'mock-token' };
const MSAL_GUARD_CONFIG = new InjectionToken('MSAL_GUARD_CONFIG');
const mockHttpClient = {};
const mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error', 'info', 'warning']);
class MockGeneralOfferTypeService {}
// Minimal mock class for SearchOfferService
class MockSearchOfferService {}

describe('TemplateBatchActionComponent', () => {
  let fixture: ComponentFixture<TemplateBatchActionComponent>;
  let comp: TemplateBatchActionComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TemplateBatchActionComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: FacetItemService, useClass: MockFacetItemService },
        { provide: BulkUpdateService, useValue: mockBulkUpdateService },
        { provide: BsModalService, useClass: MockBsModalService },
        { provide: SearchOfferRequestService, useValue: mockSearchOfferRequestService },
        { provide: HttpClient, useValue: mockHttpClient },
        { provide: AuthService, useValue: mockAuthService },
        { provide: MSAL_GUARD_CONFIG, useValue: {} },
        { provide: PermissionsService, useClass: MockPermissionsService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: GeneralOfferTypeService, useClass: MockGeneralOfferTypeService },
        { provide: SearchOfferService, useClass: MockSearchOfferService },
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;
    comp.popupRef = mockPopoverRef as any;
    comp.batchType = 'BATCH';
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(comp).toBeTruthy();
  });

  it('should call getBatchActionsFromRules and set pcSelected on ngOnInit', () => {
    const facetItemService = TestBed.inject(FacetItemService) as any;
    // Set the value that the component will use
    facetItemService.templateProgramCodeSelected = 'BPD';
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;
    comp.popupRef = mockPopoverRef as any;
    comp.batchType = 'BATCH';
    spyOn(comp, 'getBatchActionsFromRules');
    comp.ngOnInit();
    expect(comp.pcSelected).toBe('BPD');
    expect(comp.getBatchActionsFromRules).toHaveBeenCalledWith('BPD', jasmine.anything());
  });

  it('should return correct payloadQuery', () => {
    // Set up the mockBulkUpdateService before component creation
    mockBulkUpdateService.requestIdArr = [1, 2, 3];
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;
    comp.batchType = 'BATCH';
    comp.pcSelected = 'PCODE';
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');
    // The component may use requestIdArr or another property, so match the actual call
    const expectedPayload = mockBulkUpdateService.requestIdArr || [];
    const result = comp.payloadQuery;
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: expectedPayload,
      batchType: 'BATCH',
      key: 'requestId',
      progrmCd: 'PCODE',
      progrmCdKey: 'programCode',
      facetPage: 'template',
    });
    expect(result).toBe('QUERY');
  });

  it('should call popupRef.hide, set action, and call onClickBaseAction on onClickActionElement', () => {
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';
    const action = { key: 'copy' };
    comp.onClickActionElement(action);
    expect(mockPopoverRef.hide).toHaveBeenCalled();
    expect(comp.action).toBe(action);
    expect(comp.onClickBaseAction).toHaveBeenCalledWith(action, 'QUERY', 'template', 'PCODE');
  });

  it('should not throw if popupRef is undefined in onClickActionElement', () => {
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');
    comp.popupRef = undefined;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';
    const action = { key: 'copy' };
    expect(() => comp.onClickActionElement(action)).not.toThrow();
    expect(comp.action).toBe(action);
    expect(comp.onClickBaseAction).toHaveBeenCalled();
  });
});
