import { ComponentFixture, TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { BaseInputSearchComponent } from './base-input-search.component';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormControl, UntypedFormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { of, Subject, BehaviorSubject } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, Injector } from '@angular/core';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonService } from '@appServices/common/common.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { LoaderService } from '@appServices/common/loader.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CONSTANTS } from '@appConstants/constants';
import * as getControlByNameUtil from '@appUtilities/getControlByName';
import * as resetFormControlErrorUtil from '@appUtilities/resetFormControlError';
import * as updateTreeValidityUtil from '@appUtilities/updateValueValidatityForTree';

// Mock services
class MockBaseInputSearchService {
  inputOptions = [
    {
      field: 'mockFieldWithSubLevels',
      label: 'Mock Field With Sub Levels',
      defaultSelect: true,
      elements: [
        {
          field: 'mainSelect',
          type: 'select',
          options: [
            { label: 'Default Sub Option', bindValue: 'defaultSubVal', defaultSelect: true, elements: [{ field: 'subLevelElement', value: 'subDefault' }] }
          ]
        }
      ]
    },
    { field: 'periodWeekField', label: 'Period Week Field', defaultSelect: false, elements: [{ field: 'periodElement', value: '' }], dropDownOptionsFromApi: { methodName: 'getPeriodOptions', optionLevel: 'selectedOption' } },
    { field: 'featureFlaggedField', label: 'Feature Flagged Field', defaultSelect: false, featureFlag: 'testFeatureFlag', elements: [{ field: 'featureElement', value: '' }] },
    { field: 'linkedField', label: 'Linked Field', isLinkedObj: true, query: [], linkedWith: 'sourceField', elements: [] },
    { field: 'sourceField', label: 'Source Field', elements: [], linkedTo: 'linkedField', linkValue: 'linkedValue123' },
    { field: 'productGroupType', label: 'Product Group Type', defaultSelect: false, elements: [{ field: 'pgType', value: 'BASE' }] }
  ];
  inputFormGrpValue: any;
  setActiveCurrentSearchType = jasmine.createSpy('setActiveCurrentSearchType');
  getInputFieldSelected = jasmine.createSpy('getInputFieldSelected').and.returnValue({});
  setChipForField = jasmine.createSpy('setChipForField');
  generateQueryForOptions = jasmine.createSpy('generateQueryForOptions').and.returnValue(false);
  getActiveCurrentSearchType = jasmine.createSpy('getActiveCurrentSearchType').and.returnValue('testSearchType');
  getDataForInputSearch = jasmine.createSpy('getDataForInputSearch').and.returnValue(of({ data: 'searchData' }));
  setFormQuery = jasmine.createSpy('setFormQuery');
  formQuery = jasmine.createSpy('formQuery').and.returnValue('query');
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
  getLastPeriodOptions = jasmine.createSpy('getLastPeriodOptions').and.returnValue(of([{ periodWeek: '202501', periodId: 1 }, { periodWeek: '202502', periodId: 2 }]));
  postDataForInputSearch = jasmine.createSpy('postDataForInputSearch');
  setQueryWithOrFilter = jasmine.createSpy('setQueryWithOrFilter');
  queryWithOrFilter = 'queryWithOrFilter';
  inputSearchChip = function(val) { return {}; };
  currentRouter = 'mockRouter';
  mockRouter = new BehaviorSubject<any>(null);
}

class MockSearchUsersService {
  getUsers = jasmine.createSpy('getUsers').and.returnValue(of([{ firstName: 'John', lastName: 'Doe', userId: 'john.doe' }]));
}

class MockCommonSearchService {
  isFiltered = false;
  isStoreIdUserSearchEnabled = false;
  fetchDefaultOptions = jasmine.createSpy('fetchDefaultOptions');
  getFilterOption = jasmine.createSpy('getFilterOption').and.returnValue({});
  getInputSearchChip = jasmine.createSpy('getInputSearchChip').and.returnValue({});
  setInputSearchChip = jasmine.createSpy('setInputSearchChip');
  inputSearchChip = { sourceField: 'someValue' }; // For linked options test
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
}

class MockCommonService {
  getPeriodWeeks = jasmine.createSpy('getPeriodWeeks').and.returnValue(of(['202501', '202502']));
}

class MockCommonRouteService {
  currentActivatedRoute = 'mockRoute';
  currentRoute = 'mockRoute';
  debugFlagEnabledRoutes = [];
  router = { navigate: jasmine.createSpy('navigate') } as any;
  acivatedRoute = {};
  currentRouter = 'mockRoute';
  routerPage = '';
  isDebugTrueExists = false;
  isBpdReqPage = false;
  getCurrentRoute = jasmine.createSpy('getCurrentRoute').and.returnValue('mockRoute');
  getCurrentActivatedRoute = jasmine.createSpy('getCurrentActivatedRoute').and.returnValue('mockActivatedRoute');
  getCurrentRouteName = jasmine.createSpy('getCurrentRouteName');
  getCurrentRoutePath = jasmine.createSpy('getCurrentRoutePath');
  getCurrentRouteParams = jasmine.createSpy('getCurrentRouteParams');
}

class MockLoaderService {}
class MockBaseSavedSearchService {}
class MockFacetItemService {}
class MockFeatureFlagsService {
  isFeatureFlagEnabled = jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true);
}

fdescribe('BaseInputSearchComponent', () => {
  let component: BaseInputSearchComponent;
  let fixture: ComponentFixture<BaseInputSearchComponent>;
  let mockAppInjector: jasmine.SpyObj<Injector>;
  let mockBaseInputSearchService: MockBaseInputSearchService;
  let mockSearchUsersService: MockSearchUsersService;
  let mockCommonSearchService: MockCommonSearchService;
  let mockCommonService: MockCommonService;
  let mockCommonRouteService: MockCommonRouteService;
  let mockLoaderService: MockLoaderService;
  let mockBaseSavedSearchService: MockBaseSavedSearchService;
  let mockFacetItemService: MockFacetItemService;
  let mockFeatureFlagsService: MockFeatureFlagsService;
  let fb: UntypedFormBuilder;
  
  beforeEach(async () => {
    // Create fresh mock instances before each test
    mockBaseInputSearchService = new MockBaseInputSearchService();
    mockSearchUsersService = new MockSearchUsersService();
    mockCommonSearchService = new MockCommonSearchService();
    mockCommonService = new MockCommonService();
    mockCommonRouteService = new MockCommonRouteService();
    mockLoaderService = new MockLoaderService();
    mockBaseSavedSearchService = new MockBaseSavedSearchService();
    mockFacetItemService = new MockFacetItemService();
    mockFeatureFlagsService = new MockFeatureFlagsService();

    // Mock utility functions
    spyOn(getControlByNameUtil, 'getControlByName').and.callFake(({ rootControl, controlName }) => {
      if (rootControl?.get('inputGroups')) {
        const inputGroups = rootControl.get('inputGroups') as UntypedFormArray;
        for (let i = 0; i < inputGroups.length; i++) {
          const group = inputGroups.at(i);
          if (group.get(controlName)) {
            return [group.get(controlName)];
          }
        }
      }
      return null;
    });

    spyOn(resetFormControlErrorUtil, 'resetFormControlError').and.stub();
    spyOn(updateTreeValidityUtil, 'updateTreeValidity').and.stub();

    // Mock AppInjector
    mockAppInjector = jasmine.createSpyObj('Injector', ['get']);
    spyOn(AppInjector, 'getInjector').and.returnValue(mockAppInjector);

    mockAppInjector.get.and.callFake((token: any) => {
      if (token === UntypedFormBuilder) {
        return new UntypedFormBuilder(); // Return a real instance
      }
      if (token === BaseInputSearchService) return mockBaseInputSearchService;
      if (token === SearchUsersService) return mockSearchUsersService;
      if (token === CommonSearchService) return mockCommonSearchService;
      if (token === CommonService) return mockCommonService;
      if (token === CommonRouteService) return mockCommonRouteService;
      if (token === LoaderService) return mockLoaderService;
      if (token === BaseSavedSearchService) return mockBaseSavedSearchService;
      if (token === FacetItemService) return mockFacetItemService;
      if (token === FeatureFlagsService) return mockFeatureFlagsService;
      return undefined;
    });

    await TestBed.configureTestingModule({
      declarations: [BaseInputSearchComponent],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BaseInputSearchComponent);
    component = fixture.componentInstance;
    fb = TestBed.inject(UntypedFormBuilder);

    // Set @Input properties
    component.currentSearchType = 'testSearchType';
    component.isHideSavedSearch = false;

    // Initialize form for all tests to avoid null errors
    component.inputFormGroup = fb.group({
      inputSelected: [mockBaseInputSearchService.inputOptions[0].field],
      savedSearchName: [''],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });
  });

  // Helper to initialize component and run ngOnInit
  function initializeComponent() {
    // Manually call ngOnInit instead of using fixture.detectChanges()
    // to avoid potential issues with the fixture
    component.ngOnInit();
  }

  it('should create the component', () => {
    initializeComponent();
    expect(component).toBeTruthy();
  });

  it('should initialize with the correct search type', () => {
    spyOn(component, 'getInitialOptionsList').and.callThrough();
    
    initializeComponent();
    
    expect(component.getInitialOptionsList).toHaveBeenCalled();
    expect(mockBaseInputSearchService.setActiveCurrentSearchType).toHaveBeenCalledWith('testSearchType');
  });

  it('should include feature flagged options when flag is enabled', () => {
    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
    
    initializeComponent();
    
    const featureFlaggedOption = component.searchOptions.find(opt => opt.field === 'featureFlaggedField');
    expect(featureFlaggedOption).toBeDefined();
  });

  it('should exclude feature flagged options when flag is disabled', () => {
    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(false);
    
    initializeComponent();
    
    const featureFlaggedOption = component.searchOptions?.find(opt => opt.field === 'featureFlaggedField');
    expect(featureFlaggedOption).toBeUndefined();
  });

  it('should exclude multiple feature flagged options when flags are disabled', () => {
    // Setup multiple feature-flagged options
    const testOptions = [
      { field: 'regular', label: 'Regular Field' },
      { field: 'feature1', label: 'Feature 1', featureFlag: 'flag1' },
      { field: 'feature2', label: 'Feature 2', featureFlag: 'flag2' }
    ];
    component.inputSearchOptions = testOptions;
    
    // Mock feature flags to return false
    mockFeatureFlagsService.isFeatureFlagEnabled.and.callFake(flag => {
      return false; // Disable all flags
    });
    
    // Call the method that would filter based on feature flags
    component.getInitialOptionsList();
    
    // Only non-feature-flagged options should remain
    expect(component.searchOptions.length).toBe(1);
    expect(component.searchOptions[0].field).toBe('regular');
  });

  it('should handle getPeriodWeeks with valid term', () => {
    const result = component.getPeriodWeeks('202501');
    
    expect(mockCommonService.getPeriodWeeks).toHaveBeenCalledWith('202501');
    expect(result.subscribe).toBeDefined();
  });

  it('should handle getPeriodWeeks with null term', () => {
    const result = component.getPeriodWeeks(null);
    
    expect(mockCommonService.getPeriodWeeks).not.toHaveBeenCalled();
    expect(result).toBeDefined();
  });

  it('should handle removeWordAfterSpace correctly', () => {
    expect(component.removeWordAfterSpace('one two')).toBe('one');
    expect(component.removeWordAfterSpace('one two three')).toBe('one two');
    expect(component.removeWordAfterSpace('single')).toBe('single');
    expect(component.removeWordAfterSpace('')).toBe('');
    expect(component.removeWordAfterSpace(null)).toBe(null);
  });

  it('should handle getUsers with valid term', () => {
    const result = component.getUsers('John');
    
    expect(mockSearchUsersService.getUsers).toHaveBeenCalledWith('John');
    expect(result.subscribe).toBeDefined();
  });

  it('should handle getUsers with null term', () => {
    const result = component.getUsers(null);
    
    expect(mockSearchUsersService.getUsers).not.toHaveBeenCalled();
    expect(result).toBeDefined();
  });

  it('should return the correct typeaheadApi function for various fields', () => {
    component.optionListSelected = { field: 'createUserId' };
    expect(typeof component.getTypeAheadApis()).toBe('function');
    
    component.optionListSelected = { field: 'periodWeek' };
    expect(typeof component.getTypeAheadApis()).toBe('function');
    
    component.optionListSelected = { field: 'nonExistingField' };
    expect(component.getTypeAheadApis()).toBeUndefined();
  });

  it('should handle typeahead subscriptions correctly', fakeAsync(() => {
    spyOn(component, 'setTypeAheadList').and.stub();
    const mockApiFn = jasmine.createSpy().and.returnValue(of(['test item']));
    spyOn(component, 'getTypeAheadApis').and.returnValue(mockApiFn);
    
    component.initSubscribes();
    component.typeahead$.next('test');
    tick(300); // For debounceTime
    
    expect(mockApiFn).toHaveBeenCalledWith('test');
    expect(component.setTypeAheadList).toHaveBeenCalledWith(['test item']);
    
    // Test distinctUntilChanged
    component.typeahead$.next('test'); // Same value
    tick(300);
    expect(mockApiFn).toHaveBeenCalledTimes(1); // Should not call again
    
    component.typeahead$.next('new test'); // Different value
    tick(300);
    expect(mockApiFn).toHaveBeenCalledTimes(2); // Should call again
    
    flush();
  }));

  it('should set users list for typeahead correctly', () => {
    component.setUsersListForTypeAhead([
      { firstName: 'John', lastName: 'Doe', userId: 'jdoe' },
      { firstName: 'Jane', lastName: '', userId: 'jane' },
      { firstName: '', lastName: 'Smith', userId: 'smith' }
    ]);
    
    expect(component.typeaheadArrayList).toEqual([
      { name: 'John Doe', id: 'jdoe' },
      { name: 'Jane ', id: 'jane' },
      { name: ' Smith', id: 'smith' }
    ]);
  });

  it('should set period list for typeahead correctly', () => {
    component.setPeriodListForTypeahead(['202501', '202502']);
    
    expect(component.typeaheadArrayList).toEqual([
      { name: '202501', id: '202501' },
      { name: '202502', id: '202502' }
    ]);
  });

  it('should handle setTypeAheadList for different field types', () => {
    // For users
    component.optionListSelected = { field: 'createUserId' };
    const mockUsers = [{ firstName: 'Test', lastName: 'User', userId: 'tuser' }];
    spyOn(component, 'setUsersListForTypeAhead');
    
    component.setTypeAheadList(mockUsers);
    expect(component.setUsersListForTypeAhead).toHaveBeenCalledWith(mockUsers);
    
    // For periods
    component.optionListSelected = { field: 'periodWeek' };
    const mockPeriods = ['202501', '202502'];
    spyOn(component, 'setPeriodListForTypeahead');
    
    component.setTypeAheadList(mockPeriods);
    expect(component.setPeriodListForTypeahead).toHaveBeenCalledWith(mockPeriods);
  });

  it('should get class based on column class or page type', () => {
    // With columnClass provided
    expect(component.getClass({ columnClass: 'test-class' })).toBe('test-class');
    
    // Based on page type
    mockCommonRouteService.currentRouter = CONSTANTS.TEMPLATE + '/some-path';
    expect(component.getClass({})).toBe('col-6 pr-6');
    
    mockCommonRouteService.currentRouter = 'other/path';
    expect(component.getClass({})).toBe('col-5');
  });

  it('should correctly identify action log pages', () => {
    mockCommonRouteService.currentRouter = CONSTANTS.ACTION_LOG + '/details';
    expect(component.actionLogWidth).toBeTrue();
    
    mockCommonRouteService.currentRouter = CONSTANTS.IMPORT_LOG_BPD;
    expect(component.actionLogWidth).toBeTrue();
    
    mockCommonRouteService.currentRouter = 'other/path';
    expect(component.actionLogWidth).toBeFalse();
  });

  it('should handle keypress events correctly', () => {
    spyOn(component, 'searchClickHandler').and.stub();
    
    // Enter key
    const enterEvent = new KeyboardEvent('keypress');
    Object.defineProperty(enterEvent, 'which', { value: 13 });
    spyOn(enterEvent, 'preventDefault');
    
    component.onKeypress(enterEvent, {});
    expect(component.searchClickHandler).toHaveBeenCalled();
    expect(enterEvent.preventDefault).toHaveBeenCalled();
    
    // Other key
    const otherEvent = new KeyboardEvent('keypress');
    Object.defineProperty(otherEvent, 'which', { value: 65 }); // 'A'
    spyOn(otherEvent, 'preventDefault');
    
    spyOn(component, 'searchClickHandler');
    component.onKeypress(otherEvent, {});
    expect(component.searchClickHandler).not.toHaveBeenCalled();
    expect(otherEvent.preventDefault).not.toHaveBeenCalled();
  });

  it('should handle onPasteSearch by calling onKeypress', () => {
    spyOn(component, 'onKeypress');
    const event = new Event('paste');
    const option = { field: 'test' };
    
    component.onPasteSearch(event, option);
    expect(component.onKeypress).toHaveBeenCalledWith(event, option);
  });

  it('should check if comma is allowed in input fields', () => {
    component.isCommaNotAllowed = ['upc', 'hhid'];
    component.showSearchError = '';
    
    // Comma not allowed
    const result1 = component.isCheckedCommaAllowed('upc', [{ upc: '123,456' }]);
    expect(result1).toBeTrue();
    expect(component.showSearchError).toBe(component.searchError.upc);
    
    // Comma allowed
    component.showSearchError = '';
    const result2 = component.isCheckedCommaAllowed('other', [{ other: '123,456' }]);
    expect(result2).toBeUndefined();
    expect(component.showSearchError).toBe('');
  });

  it('should handle getMinDateBasedOnProperty', () => {
    const testDate = new Date(2025, 0, 1);
    component.rangeEndDate = testDate;
    
    expect(component.getMinDateBasedOnProperty('To')).toBe(testDate);
    expect(component.getMinDateBasedOnProperty('From')).toBeUndefined();
  });

  it('should handle dateChangeHandler correctly', () => {
    // Create form structure for testing
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [new Date(2025, 0, 1)] })])
    });
    
    const newDate = new Date(2025, 0, 15);
    component.dateChangeHandler(newDate);
    
    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value).toEqual(newDate);
  });

  it('should handle dateChangeHandler with date comparison', () => {
    // Create form structure with a future date in 'To' field
    const futureDate = new Date(2026, 0, 1);  // Jan 1, 2026
    const newDate = new Date(2025, 6, 1);     // July 1, 2025 (earlier than futureDate)
    
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [futureDate] })])
    });
    
    // New date is earlier than existing To date, so To date should not be updated
    component.dateChangeHandler(newDate);
    
    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value)
      .toEqual(futureDate); // should not change because newDate < futureDate
  });

  it('should handle dateChangeHandler with non-range input groups', () => {
    // Create form with non-Range value in input groups
    const newDate = new Date(2025, 0, 15);
    
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['NotRange'] })]), // Not "Range"
      inputGroupsLevel: fb.array([fb.group({ To: [new Date(2025, 0, 1)] })])
    });
    
    // Should set rangeEndDate but not update To value due to non-Range input type
    component.dateChangeHandler(newDate);
    
    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value)
      .toEqual(new Date(2025, 0, 1)); // Should remain unchanged
  });

  it('should handle dateChangeHandler with null input groups or level', () => {
    const newDate = new Date(2025, 0, 15);
    
    // Form with no inputGroups
    component.inputFormGroup = fb.group({
      inputSelected: ['testField']
      // No inputGroups or inputGroupsLevel
    });
    
    // Should not throw error when groups/levels are missing
    expect(() => {
      component.dateChangeHandler(newDate);
    }).not.toThrow();
    
    expect(component.rangeEndDate).toEqual(newDate);
  });

  it('should get field errors correctly', () => {
    // Setup
    component.inputFormGroup = fb.group({
      inputSelected: ['test'],
      inputGroups: fb.array([
        fb.group({ fieldWithError: ['', Validators.required] })
      ])
    });
    
    const control = component.inputFormGroup.get('inputGroups').get('0').get('fieldWithError');
    control.markAsTouched();
    control.updateValueAndValidity();
    
    // Mock getControlByName
    (getControlByNameUtil.getControlByName as jasmine.Spy).and.returnValue([control]);
    
    // Test
    const errors = component.getFieldErrors('fieldWithError');
    expect(errors).toContain('required');
  });

  it('should handle getControl correctly', () => {
    // Setup
    component.inputFormGroup = fb.group({
      inputGroups: fb.array([
        fb.group({ testField: ['test value'] })
      ])
    });
    
    const testControl = component.inputFormGroup.get('inputGroups').get('0').get('testField');
    
    // Mock getControlByName
    (getControlByNameUtil.getControlByName as jasmine.Spy).and.returnValue([testControl]);
    
    // Test
    const result = component.getControl('testField');
    expect(result).toBe(testControl);
  });

  it('should handle getErrorsForField correctly', () => {
    // Untouched control with errors
    const control1 = new UntypedFormControl();
    control1.setErrors({ required: true });
    control1.markAsUntouched();
    
    const result1 = component.getErrorsForField(control1);
    expect(result1).toBeTrue();
    expect(control1.errors).toEqual({ required: true });
    
    // Touched control with errors - should clear errors
    const control2 = new UntypedFormControl();
    control2.setErrors({ required: true });
    control2.markAsTouched();
    
    const result2 = component.getErrorsForField(control2);
    expect(result2).toBeFalse();
    expect(control2.errors).toBeNull();
    
    // Control without errors
    const control3 = new UntypedFormControl();
    
    const result3 = component.getErrorsForField(control3);
    expect(result3).toBeFalse();
  });

  it('should handle setFieldValidator to add validators', () => {
    const formArray = fb.array([
      fb.group({ test1: [''], test2: [''] }),
      fb.group({ test3: [''] })
    ]);
    
    component.setFieldValidator(formArray);
    
    expect(formArray.at(0).get('test1').validator).toBeDefined();
    expect(formArray.at(0).get('test2').validator).toBeDefined();
    expect(formArray.at(1).get('test3').validator).toBeDefined();
  });

  it('should handle setValidators with null inputGroupsLevel', () => {
    const inputGroupsArr = fb.array([fb.group({ field: [''] })]);
    spyOn(component, 'setFieldValidator');
    
    component.setValidators(inputGroupsArr, null);
    
    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsArr);
    expect(component.setFieldValidator).toHaveBeenCalledTimes(1);
  });

  it('should handle setValidators with both arrays', () => {
    const inputGroupsArr = fb.array([fb.group({ field1: [''] })]);
    const inputGroupsLevelArr = fb.array([fb.group({ field2: [''] })]);
    spyOn(component, 'setFieldValidator');
    
    component.setValidators(inputGroupsArr, inputGroupsLevelArr);
    
    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsArr);
    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsLevelArr);
    expect(component.setFieldValidator).toHaveBeenCalledTimes(2);
  });

  it('should validate form correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['field'],
      inputGroups: fb.array([fb.group({ test: ['value'] })]),
      inputGroupsLevel: fb.array([])
    });
    
    spyOn(component, 'setValidators');
    
    const result = component.isFormValid();
    
    expect(result).toBeTrue();
    expect(resetFormControlErrorUtil.resetFormControlError).toHaveBeenCalledWith(component.inputFormGroup);
    expect(component.inputFormGroup.valid).toBeTrue();
    expect(component.setValidators).toHaveBeenCalled();
    expect(updateTreeValidityUtil.updateTreeValidity).toHaveBeenCalled();
  });

  it('should handle onCheckChanged correctly', () => {
    component.onCheckChanged(true);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeTrue();
    
    component.onCheckChanged(false);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeFalse();
  });

  it('should handle searchClickHandler with invalid form', () => {
    spyOn(component, 'isFormValid').and.returnValue(false);
    const result = component.searchClickHandler();
    expect(result).toBeTrue();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler when comma is not allowed', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(true);
    
    const result = component.searchClickHandler();
    expect(result).toBeTrue();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with no input value', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    
    component.inputFormGroup = fb.group({
      inputSelected: ['mockField'],
      inputGroups: fb.array([fb.group({ mockField: [''] })])
    });
    
    const result = component.searchClickHandler();
    expect(result).toBeFalse();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with valid inputs', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    component.inputFormGroup = fb.group({
      inputSelected: ['mockField'],
      inputGroups: fb.array([fb.group({ mockField: ['value'] })])
    });
    
    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    
    const result = component.searchClickHandler();
    
    expect(mockBaseInputSearchService.getInputFieldSelected).toHaveBeenCalled();
    expect(mockBaseInputSearchService.generateQueryForOptions).toHaveBeenCalled();
    expect(mockCommonSearchService.fetchDefaultOptions).toHaveBeenCalled();
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalled();
    expect(mockBaseInputSearchService.postDataForInputSearch).toHaveBeenCalled();
  });

  it('should handle searchClickHandler with complete workflow', () => {    // Setup
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    
    component.inputFormGroup = fb.group({
      inputSelected: ['mockField'],
      inputGroups: fb.array([fb.group({ mockField: ['value'] })]),
      inputGroupsLevel: fb.array([])
    });
      mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);
    mockBaseInputSearchService.getActiveCurrentSearchType.and.returnValue('testType');    mockBaseInputSearchService.queryWithOrFilter = 'testQueryFilter';    const originalInputSearchChip = mockBaseInputSearchService.inputSearchChip;
    // Instead of assigning a function directly, create a jasmine spy
    mockBaseInputSearchService.inputSearchChip = jasmine.createSpy('inputSearchChip').and.returnValue({ someFieldKey: { someLabel: 'someChipValue' } });
    mockBaseInputSearchService.setQueryWithOrFilter = jasmine.createSpy('setQueryWithOrFilter');
    spyOn(component, 'removeFormControl').and.callThrough();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    
    // Execute
    const result = component.searchClickHandler();
    
    // Verify
    expect(mockBaseInputSearchService.formQuery).toHaveBeenCalled();
    expect(mockBaseInputSearchService.setFormQuery).toHaveBeenCalledWith('query');
    expect(mockBaseInputSearchService.setQueryWithOrFilter).toHaveBeenCalledWith('testQueryFilter');
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalled();
    expect(component.removeFormControl).toHaveBeenCalledWith('inputGroups');
    expect(component.removeFormControl).toHaveBeenCalledWith('inputGroupsLevel');
    expect(component.createArrayFormControl).toHaveBeenCalled();
    expect(component.buildArrayElementControl).toHaveBeenCalled();
    expect(mockBaseInputSearchService.postDataForInputSearch).toHaveBeenCalledWith(true);
  });

  it('should handle productGroupType with BASE value', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    
    component.inputFormGroup = fb.group({
      inputSelected: ['productGroupType'],
      inputGroups: fb.array([fb.group({ productGroupType: ['BASE'] })])
    });
    
    mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);
    mockBaseInputSearchService.inputFormGrpValue = { inputSelected: 'productGroupType' };
    
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    // Execute
    component.searchClickHandler();
    
    // Verify that fetchDefaultOptions was called with isBaseTypeSelected=true
    expect(mockCommonSearchService.fetchDefaultOptions).toHaveBeenCalledWith({
      key: jasmine.any(String),
      currentRouter: jasmine.any(String),
      isBaseTypeSelected: true
    });
  });

  it('should handle setOptionsFromApi for multiple different methods', () => {
    // Test with getPeriodOptions
    spyOn(component, 'getPeriodOptions');
    component.setOptionsFromApi('getPeriodOptions', 'selectedOption');
    expect(component.getPeriodOptions).toHaveBeenCalledWith('selectedOption');
    
    // Test with a method that doesn't exist
    spyOn(console, 'error').and.stub(); // Prevent error output in test
    expect(() => component.setOptionsFromApi('nonExistentMethod', 'someOption')).not.toThrow();
    
    // Test with null parameters
    expect(() => component.setOptionsFromApi(null, 'selectedOption')).not.toThrow();
    expect(() => component.setOptionsFromApi('getPeriodOptions', null)).not.toThrow();
    expect(() => component.setOptionsFromApi(null, null)).not.toThrow();
  });

  it('should handle getTypeAheadApis for all supported field types', () => {
    // Test for user-related fields
    const userFields = ['createUserId', 'createdBy', 'updatedBy', 'updatedByUser', 'createdByUser', 'updatedUserId'];
    
    userFields.forEach(field => {
      component.optionListSelected = { field };
      const api = component.getTypeAheadApis();
      expect(api).toBeDefined();
      expect(typeof api).toBe('function');
    });
    
    // Test for period-related fields
    const periodFields = ['periodWeek', 'lastPeriodCreated'];
    
    periodFields.forEach(field => {
      component.optionListSelected = { field };
      const api = component.getTypeAheadApis();
      expect(api).toBeDefined();
      expect(typeof api).toBe('function');
    });
    
    // Test for non-existing field type
    component.optionListSelected = { field: 'nonExistingField' };
    expect(component.getTypeAheadApis()).toBeUndefined();
  });

  it('should handle feature-flagged options with dynamic flag values', () => {
    // Setup multiple feature-flagged options with different flags
    component.inputSearchOptions = [
      { field: 'regular', label: 'Regular Field', elements: [] },
      { field: 'feature1', label: 'Feature 1', featureFlag: 'flag1', elements: [] },
      { field: 'feature2', label: 'Feature 2', featureFlag: 'flag2', elements: [] }
    ];
    
    // Mock feature flags to return true for flag1 and false for flag2
    mockFeatureFlagsService.isFeatureFlagEnabled.and.callFake(flag => {
      return flag === 'flag1'; // Only enable flag1
    });
    
    // Reset searchOptions and call method
    component.searchOptions = undefined;
    component.getInitialOptionsList();
    
    // Should include regular field and flag1 field but not flag2
    expect(component.searchOptions.length).toBe(2);
    expect(component.searchOptions.some(opt => opt.field === 'regular')).toBeTrue();
    expect(component.searchOptions.some(opt => opt.field === 'feature1')).toBeTrue();
    expect(component.searchOptions.some(opt => opt.field === 'feature2')).toBeFalse();
  });

  it('should properly handle getDataForInputSearch subscription', () => {
    const mockResponse = { data: 'testData' };
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService.currentRouter = 'testRouter';
    mockBaseInputSearchService['testRouter'] = new BehaviorSubject(null);
    spyOn(mockBaseInputSearchService['testRouter'], 'next');
    
    component.getDataForInputSearch();
    
    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect(mockBaseInputSearchService['testRouter'].next).toHaveBeenCalledWith(mockResponse);
  });

  it('should handle the input options array mapped properly in getInitialOptionsList', () => {
    // Set up a test case with various option types
    mockBaseInputSearchService.inputOptions = [
      {
        field: 'option1',
        label: 'Option 1',
        defaultSelect: true,
        elements: [{ field: 'subOption1', value: 'subValue1' }]
      },
      {
        field: 'option2',
        label: 'Option 2 with Feature Flag',
        defaultSelect: false,
        featureFlag: 'testFlag',
        elements: [{ field: 'subOption2', value: 'subValue2' }]
      },      {
        field: 'option3',
        label: 'Option 3 Hidden',
        defaultSelect: false,
        // Remove isHidden property as it's causing type issues
        elements: [{ field: 'subOption3', value: 'subValue3' }]
      }
    ];
    
    // Feature flag is enabled for the test
    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
    
    // We need to create form controls during the test
    spyOn(component, 'createFormControl').and.callThrough();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    // Call the method
    component.getInitialOptionsList();
    
    // Verify the transformed searchOptions
    expect(component.searchOptions.length).toBe(3); // All 3 options should be included
    expect(component.searchOptions[0].field).toBe('option1');
    expect(component.searchOptions[1].field).toBe('option2');
    expect(component.searchOptions[2].field).toBe('option3');
    
    // Verify the default option was selected
    expect(component.selectedOption).toBeDefined();
    expect(component.selectedOption.field).toBe('option1');
    expect(component.createFormControl).toHaveBeenCalledWith('option1');
  });

  it('should handle getUniqueListBy with empty array', () => {
    const result = component.getUniqueListBy([], 'anyKey');
    expect(result).toEqual([]);
  });
  it('should handle setOptions with proper formatting and sorting', () => {
    const selectedOption = {
      elements: [{
        options: [{
          defaultSelect: true,
          label: 'Default Option'
        }]
      }]
    };

    const response = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 },
      { periodWeek: '202503', periodId: 3 },
      { periodWeek: '202501', periodId: 1 } // Duplicate that should be filtered
    ];

    component.setOptions(selectedOption, response);

    // Verify options were formatted and sorted properly
    const resultOptions = selectedOption.elements[0].options;
    expect(resultOptions.length).toBe(4); // Default + 3 unique periods
    
    // Default option should be first
    expect(resultOptions[0].defaultSelect).toBeTrue();
    
    // Check sorting - should be in descending order by periodId
    expect((resultOptions[1] as any).bindValue).toBe('202503');
    expect((resultOptions[2] as any).bindValue).toBe('202502');
    expect((resultOptions[3] as any).bindValue).toBe('202501');
  });
  
  it('should handle getPeriodOptions with subscription', () => {
    spyOn(component, 'setOptions');
    const mockResponse = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockResponse));
    
    const mockSelectedOption = { 
      elements: [{ field: 'periodElement', options: [] }] 
    };
    
    component.getPeriodOptions(mockSelectedOption);
    
    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(mockSelectedOption, mockResponse);
  });

  it('should handle getDataForInputSearch', () => {
    // Setup
    const mockResponse = { data: 'testData' };
    const mockSubject = new Subject();
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService[mockBaseInputSearchService.currentRouter] = mockSubject;
    spyOn(mockSubject, 'next').and.callThrough();
    
    // Execute
    component.getDataForInputSearch();
    
    // Verify
    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect(mockSubject.next).toHaveBeenCalledWith(mockResponse);
  });
  
  it('should handle getUniqueListBy correctly', () => {
    // Setup
    const testArray = [
      { id: 1, name: 'Test 1' },
      { id: 2, name: 'Test 2' },
      { id: 1, name: 'Test 3' } // Duplicate id
    ];
    
    // Execute
    const result = component.getUniqueListBy(testArray, 'id');
    
    // Verify - should remove duplicates based on id
    expect(result.length).toBe(2);
    // Convert result to a safer format for testing
    const names = result.map(item => (item as any).name);
    expect(names).toContain('Test 1');
    expect(names).toContain('Test 2');
    expect(names).not.toContain('Test 3'); // This was a duplicate by id
  });
  it('should correctly filter duplicate objects in getUniqueListBy', () => {
    const testArray = [
      { id: 1, name: 'Item 1', periodWeek: '202501' },
      { id: 2, name: 'Item 2', periodWeek: '202502' },
      { id: 3, name: 'Item 3', periodWeek: '202501' }, // Duplicate periodWeek
      { id: 4, name: 'Item 4', periodWeek: '202503' }
    ];
    
    component.ngOnInit();
    const result = component.getUniqueListBy(testArray, 'periodWeek');
    
    expect(result.length).toBe(3); // Should filter out duplicates
    expect(result.map(item => (item as any).periodWeek).sort()).toEqual(['202501', '202502', '202503'].sort());
  });
  
  it('should handle empty array in getUniqueListBy', () => {
    component.ngOnInit();
    const result = component.getUniqueListBy([], 'anyKey');
    
    expect(result).toEqual([]);
  });
  
  it('should correctly handle getDataForInputSearch method', () => {
    const mockResponse = { data: 'test data' };
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService.currentRouter = 'testRouter';
    mockBaseInputSearchService['testRouter'] = new BehaviorSubject(null);
    
    component.ngOnInit();
    component.getDataForInputSearch();
    
    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect(mockBaseInputSearchService['testRouter'].value).toEqual(mockResponse);
  });

  it('should correctly handle getPeriodOptions method', () => {
    const mockPeriodDetails = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockPeriodDetails));
    spyOn(component, 'setOptions').and.callThrough();
    
    component.ngOnInit();
    const selectedOption = { elements: [{ options: [{ defaultSelect: true }] }] };
    component.getPeriodOptions(selectedOption);
    
    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(selectedOption, mockPeriodDetails);
  });

  it('should correctly handle setOptions method', () => {
    const mockPeriodDetails = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 },
      { periodWeek: '202501', periodId: 1 } // Duplicate to test getUniqueListBy
    ];
    
    const selectedOption = {
      elements: [{
        options: [{
          bindLabel: 'Default Option',
          bindValue: 'default',
          field: 'default',
          defaultSelect: true
        }]
      }]
    };
    
    component.ngOnInit();
    component.setOptions(selectedOption, mockPeriodDetails);
    
    // Verify options were sorted and added with default option at beginning
    expect(selectedOption.elements[0].options.length).toBe(3); // Default + 2 unique period options
    expect(selectedOption.elements[0].options[0].defaultSelect).toBe(true);
      // Check sorting - should be in descending order by field (periodId) 
    // Convert to number for proper comparison since field is a number stored as string
    const nonDefaultOptions = selectedOption.elements[0].options.slice(1);
    expect(Number(nonDefaultOptions[0].field)).toBeGreaterThanOrEqual(Number(nonDefaultOptions[1].field));
  });
  it('should correctly handle setOptionsFromApi method', () => {
    const getPeriodOptionsSpy = spyOn(component, 'getPeriodOptions').and.callThrough();
    
    component.ngOnInit();
    // Test with valid method and option level
    component.setOptionsFromApi('getPeriodOptions', 'selectedOption');
    expect(getPeriodOptionsSpy).toHaveBeenCalledWith(component['selectedOption']);
    
    // Test with invalid method
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi('nonExistentMethod', 'selectedOption');
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
    
    // Test with invalid option level
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi('getPeriodOptions', 'nonExistentOption');
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
    
    // Test with null inputs
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi(null, null);
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
  });
});
