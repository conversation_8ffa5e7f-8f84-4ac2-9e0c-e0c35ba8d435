import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { LoaderService } from '@appServices/common/loader.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { getControlByName } from '@appUtilities/getControlByName';
import { resetFormControlError } from '@appUtilities/resetFormControlError';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { updateTreeValidity } from '@appUtilities/updateValueValidatityForTree';
import * as moment from 'moment';
import { Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { CommonService } from '../../../../services/common/common.service';


@Component({
  selector: 'app-base-input-search',
  templateUrl: "./base-input-search.component.html",
  styleUrls: ['./base-input-search.component.scss']
})
export class BaseInputSearchComponent extends UnsubscribeAdapter implements OnInit,OnChanges {
  inputSearchOptions;
  searchOptions;
  rangeEndDate: Date = new Date();
  selectedOption;
  inputFormGroup: UntypedFormGroup;
  optionListSelected: any;
  defaultListSelection:any;
  formBuilder:UntypedFormBuilder;
  colorTheme = "theme-dark-blue";
  minEndDate: Date = new Date();
  CONSTANTS = CONSTANTS;
  @Input() currentSearchType;
  @Input() isHideSavedSearch;
  public typeahead$ = new Subject<string>();
  public typeaheadArrayList = [];
  public baseInputSearchService: BaseInputSearchService;
  public _searchUsersService: SearchUsersService;
  public commonSearchService: CommonSearchService;
  public commonService: CommonService;
  public commonRouteService :CommonRouteService;
  public loaderService:LoaderService;
  public baseSavedSearchService:BaseSavedSearchService;
  public facetItemService: FacetItemService;
  public featureFlagService:FeatureFlagsService
  private typeaheadApi = {
    createUserId:this.getUsers.bind(this),
    createdBy:this.getUsers.bind(this),
    updatedBy:this.getUsers.bind(this),
    updatedByUser: this.getUsers.bind(this),
    createdByUser: this.getUsers.bind(this),
    periodWeek: this.getPeriodWeeks.bind(this),
    lastPeriodCreated: this.getPeriodWeeks.bind(this),
    updatedUserId : this.getUsers.bind(this),
  }
  typeSelectSelectedOption: any;
  inputGroupsLevelOptions: any;
  dateTypeSelection: any;
  isNameExist = false;
  isCommaNotAllowed = ["upc","hhid","cic"];
  userSavedSearchNames: any;
  showSearchError: any;
  searchError: any = {
    upc:"You can only search on one UPC or PLU at a time. Please clear UPC/PLU filter",
    hhid:"You can only search on one HHID at a time. Please clear HHID filter"
  };

  constructor() { 
    super();
    const injector = AppInjector.getInjector();
    this.formBuilder = injector.get(UntypedFormBuilder);
    this.baseInputSearchService = injector.get(BaseInputSearchService);
    this._searchUsersService = injector.get(SearchUsersService);
    this.commonSearchService = injector.get(CommonSearchService);
    this.commonService = injector.get(CommonService);
    this.commonRouteService = injector.get(CommonRouteService);
    this.loaderService = injector.get(LoaderService);
    this.baseSavedSearchService = injector.get(BaseSavedSearchService);
    this.facetItemService = injector.get(FacetItemService);
    this.featureFlagService = injector.get(FeatureFlagsService);
  }
  
  ngOnInit(): void {
    this.initSubscribes();
    this.baseInputSearchService.setActiveCurrentSearchType(this.currentSearchType);
    this.getInitialOptionsList();
  }
  getPeriodWeeks(term) {
    if(term != null) {
      term = this.removeWordAfterSpace(term);
       return this.commonService.getPeriodWeeks(term);
    } else {
      return of([]);
    }
  }
  onPasteSearch(event,selectedOption){
    this.onKeypress(event,selectedOption);
  }
  onKeypress(event,selectedOption){
    if (event.which == 13) {
      this.searchClickHandler();
      event.preventDefault();
      return false;
    }
  }
  getClass(selectedOption) {
    const { columnClass } = selectedOption;
    return columnClass ? columnClass : this.setClassBasedOnPage;
  }
  get setClassBasedOnPage() {
    const currentRoute = this.commonRouteService.currentRouter;
    return currentRoute.includes(CONSTANTS.TEMPLATE) ? "col-6 pr-6" : "col-5";
  }
  removeWordAfterSpace(term){
    //'whole some' changes to 'whole'
    const splitText = term && term.split(' ');
    if (splitText && splitText.length > 1) {
      splitText.pop();
      term = splitText.join(' ');
    }
    return term;
  }
  getUsers(term) {
    if(term != null) {
      term = this.removeWordAfterSpace(term);
      return this._searchUsersService.getUsers(term);
    } else {
      return of([]);
    }
  }
  getTypeAheadApis(){
    return this.typeaheadApi[this.optionListSelected.field];
  }
  initSubscribes(){
    this.typeahead$
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        switchMap((term) => this.getTypeAheadApis()(term))
      )
      .subscribe((items) => {
        this.setTypeAheadList(items);
      });
  }
  setUsersListForTypeAhead(items) {
    const usersArr = (items) as any[];
    this.typeaheadArrayList = usersArr.map(
      (user) => {
        return {
          name:`${user.firstName} ${user.lastName}`,
          id:user.userId
      }
      }
    );
  }
  get actionLogWidth() {
    const currentRoute = this.commonRouteService.currentRouter;
    return (currentRoute.includes(CONSTANTS.ACTION_LOG) || currentRoute.includes(CONSTANTS.IMPORT_LOG_BPD));
  }
  setTypeAheadList(items) {
    const fieldSelected = this.optionListSelected.field;
    switch(fieldSelected) {
      case 'createUserId':
      case 'createdBy':
      case 'updatedByUser':
      case 'createdByUser':
      case 'updatedBy':
      case 'updatedUserId': {
        this.setUsersListForTypeAhead(items);
        break;
      }
      case 'periodWeek' :
      case 'lastPeriodCreated' : {
        this.setPeriodListForTypeahead(items);
        break;
      }
    }
  }
  setPeriodListForTypeahead(items) {
    const periods = (items) as any[];
    this.typeaheadArrayList = periods.map(
      (period) => {
        return {
          name: period,
          id: period
        }
      }
    );
  }
  ngOnChanges(){
    // intentionally left empty
  }
  addFormControl(name, formControl, parentForm) {
    parentForm.addControl(name, formControl);
  }
  getInitialOptionsList(){
    
   this.inputSearchOptions = this.baseInputSearchService.inputOptions;
   this.searchOptions = this.inputSearchOptions?.map(inputSearch=> {
     const {field,label,defaultSelect, isHidden = false,featureFlag=null} = inputSearch;
     if(defaultSelect){
      this.defaultListSelection = inputSearch;
      this.selectedOption = this.getSelectionOptionList(inputSearch);
      this.createFormControl(inputSearch.field);
      const inputGroups =  this.createArrayFormControl('inputGroups',this.inputFormGroup);
      this.buildArrayElementControl(this.selectedOption.elements, inputGroups);
     }
     if(!featureFlag || (featureFlag && this.featureFlagService.isFeatureFlagEnabled(featureFlag)))
      return {field,label,defaultSelect, isHidden,featureFlag};
    }).filter(Boolean);
  }
  buildArrayElementControl(elements, controlsArr) {
      if (controlsArr && elements?.length) {
       
        if(elements[0].options){
          const defaultOption = elements[0].options.filter(ele=>ele.defaultSelect)[0];
          const elementOption = {[elements[0].field]: elements[0].type === "select" ?
                defaultOption.bindValue : defaultOption.label};
          if(defaultOption.elements) {
            this.inputGroupsLevelOptions = defaultOption;
          }
          Object.keys(elementOption).forEach((element:any) => {            
            controlsArr.push(this.createArrayFormRow({field:element,value:elementOption[element]}));
          });
          this.createArrayFormControl('inputGroupsLevel',this.inputFormGroup);
          this.buildArrayElementControl(defaultOption.elements, this.inputFormGroup.get("inputGroupsLevel"));
        }else{
          elements.forEach((element) => {
            controlsArr.push(this.createArrayFormRow(element));
          });
        }
       
      }
  }
  createFormGroup(formGroup): UntypedFormGroup {
    return this.formBuilder.group(formGroup);
  }
  createArrayFormRow(element): UntypedFormGroup {
    return this.formBuilder.group({
      [element.field]:element.value
    });
  }
  isCheckedCommaAllowed(inputSelected,inputGroups){
    this.showSearchError = "";
    if(this.isCommaNotAllowed.includes(inputSelected) && inputGroups?.[0]?.[inputSelected].includes(",")){
      this.showSearchError = this.searchError[inputSelected];
      return true;
    }
  }
  
  getMinDateBasedOnProperty(prop){
    if(prop ==='To'){
      return this.rangeEndDate;
    }
  }
  dateChangeHandler(event) {
    this.rangeEndDate = event;
    const { inputGroupsLevel, inputSelected, inputGroups } = this.inputFormGroup.value;
    const isRange = inputGroups?.reduce((out, ele) => { out = { ...out, ...ele }; return out; }, {})[inputSelected];
    let toVal = inputGroupsLevel?.reduce((out, ele) => { out = { ...out, ...ele }; return out; }, {})['To'];
    if (event && isRange === 'Range') {
      this.inputGroupsLevel?.controls.forEach((element, index) => {
        if (
          moment(event).valueOf() >
          moment(toVal).valueOf()
        ) {
          element?.get('To')?.setValue(event);
        }
      });
    }
  }
  getFieldErrors(controlName){
    let matchedControlsArr = getControlByName({ rootControl: this.inputFormGroup, controlName });
    const control = matchedControlsArr?.[0];
    if (control?.errors) {
      return Object.keys(control.errors);
    }
    return null;  }
  getControl(controlName){
    let matchedControlsArr = getControlByName({ rootControl: this.inputFormGroup, controlName });
    return matchedControlsArr?.[0];
  }
  getErrorsForField(control) {
    if (control?.untouched && control?.errors) {
       return true;
     } else if(control) {
        control?.setErrors(null);
        return false;
     }
   }
   setFieldValidator(group){
    for (let index = 0; index < group.length; index++) {
      const formGroup = group.at(index) as UntypedFormGroup;
      Object.keys(formGroup.controls).forEach(control=>{
       formGroup.get(control).setValidators([Validators.required])
      })
    }
   }
  setValidators(inputGroups,inputGroupsLevel){
   const inputGroupsArr = inputGroups as UntypedFormArray,
   inputGroupsLevelArr = inputGroupsLevel as UntypedFormArray;
   this.setFieldValidator(inputGroupsArr);
   inputGroupsLevelArr?.length && this.setFieldValidator(inputGroupsLevelArr);
  }
  isFormValid(){
    resetFormControlError(this.inputFormGroup);
    this.inputFormGroup.markAsUntouched();
    this.setValidators(this.inputFormGroup.get("inputGroups"),this.inputFormGroup.get("inputGroupsLevel"));
    updateTreeValidity(this.inputFormGroup as UntypedFormGroup);
    return this.inputFormGroup.valid;
  }
  onCheckChanged(val){
    this.commonSearchService.isStoreIdUserSearchEnabled = val;
  }
  searchClickHandler(){
    this.commonSearchService.isFiltered = false;
    if(!this.isFormValid()){
     return true;
    }
    const { inputGroupsLevel,inputSelected,inputGroups } = this.inputFormGroup.value;
   
    this.baseInputSearchService.inputFormGrpValue = this.inputFormGroup.value;
    if(this.isCheckedCommaAllowed(inputSelected,inputGroups)){
      return true;
    }

    let inputVal = inputGroups?.reduce((out,ele)=>{out = {...out,...ele}; return out;},{})[inputSelected];
    if(!inputVal){
      return false;
    }

    

   const  selectedField =  this.baseInputSearchService.getInputFieldSelected(inputSelected);
   const isExist = this.baseInputSearchService.generateQueryForOptions(selectedField,inputGroups,inputGroupsLevel);
   
   if(!isExist){
    
     //Reqmt: Default sort MOB ID with lowest number first, If Base type is seelected.
    let isBaseTypeSelected = false;
    if(this.baseInputSearchService.inputFormGrpValue["inputSelected"] === "productGroupType" && inputVal === 'BASE'){
      isBaseTypeSelected = true;     
    }    

    this.commonSearchService.fetchDefaultOptions({key:this.baseInputSearchService.getActiveCurrentSearchType(),
      currentRouter:this.baseInputSearchService.currentRouter, isBaseTypeSelected});

    const query = this.baseInputSearchService.formQuery(this.commonSearchService.getFilterOption(this.currentSearchType),
                  this.commonSearchService.getInputSearchOption(this.currentSearchType),
                  this.commonSearchService.getDefaultOption(this.currentSearchType),
                  this.commonSearchService.getSortOption(this.currentSearchType));

    this.baseInputSearchService.setFormQuery(query);
    const queryWithOrFilter = this.baseInputSearchService.queryWithOrFilter;
    this.baseInputSearchService.setQueryWithOrFilter(queryWithOrFilter);
    const chipValue = this.baseInputSearchService.inputSearchChip(this.inputFormGroup.value);
    this.commonSearchService.setInputSearchChip(chipValue);
 
    this.removeFormControl("inputGroups");
    this.removeFormControl("inputGroupsLevel");
  
    this.inputFormGroup.get("inputSelected").setValue(this.defaultListSelection.field);
    const inputGroups =  this.createArrayFormControl('inputGroups',this.inputFormGroup);
    this.selectedOption = this.defaultListSelection;
    this.buildArrayElementControl(this.defaultListSelection.elements, inputGroups);
    this.baseInputSearchService.postDataForInputSearch(true);
   }
   
  }


  getDataForInputSearch(){
    this.baseInputSearchService.getDataForInputSearch().subscribe((response:any)=>{
      this.baseInputSearchService[this.baseInputSearchService.currentRouter].next(response);
    })
  }
  getPeriodOptions(selectedOptionLevel) {
    this.baseInputSearchService.getLastPeriodOptions().subscribe((promoDetails: any)=> {
      if(promoDetails?.length) {
        this.setOptions(selectedOptionLevel, promoDetails);
      }
    })
  }
  getUniqueListBy(arr, key) {
    return [...new Map(arr.map(item => [item[key], item])).values()]
}
  setOptions(selectedOption, response) {

    const {elements}   = selectedOption;
    const formattedOptions = this.getUniqueListBy(response, 'periodWeek').map((promoObj:any) => {
        return {
          bindLabel: promoObj.periodWeek,
          bindValue: promoObj.periodWeek,
          field: promoObj.periodId,
          type: 'select',
          defaultSelect: false
        }
    })
    const defaultSelectOption = elements[0]?.options?.find(ele => ele.defaultSelect);
    formattedOptions.sort(function(a, b) {
      return  Number(b.field) -Number(a.field);
  });
    elements[0].options = [defaultSelectOption,...formattedOptions];
  }
  setOptionsFromApi(methodName, optionLevel) {
    if(methodName && optionLevel && this[methodName]) {
      this?.[methodName](this?.[optionLevel]);
    }
  }
  optionSelectedHandler(event){
    const {value:{inputSelected}} = this.inputFormGroup;
    this.optionListSelected = this.inputSearchOptions.filter(ele=>ele.field===inputSelected)[0];
    this.selectedOption = this.getSelectionOptionList(this.optionListSelected);
    this.setLinkedSearchOptionQuery(this.selectedOption, true);
    this.removeFormControl("inputGroups");
    this.removeFormControl("inputGroupsLevel");
    const inputGroups =  this.createArrayFormControl('inputGroups',this.inputFormGroup);
    this.buildArrayElementControl(this.selectedOption.elements, inputGroups); 
    if(this.optionListSelected["dropDownOptionsFromApi"]) {
      const { methodName, optionLevel } = this.optionListSelected["dropDownOptionsFromApi"];
      this.setOptionsFromApi(methodName, optionLevel);
    }
    this.commonSearchService.isStoreIdUserSearchEnabled = false;
  }
  getSelectedOptionFromLabel(event, optionObj, selection) {
    const { target : { options = [], selectedIndex= null}} = event;
    return options && selectedIndex ? options[selectedIndex]?.text === optionObj.label : optionObj.bindValue === selection;
  }
  optionTypeSelectSelected(event,optionSelected){
    const {inputSelected,inputGroups:[optionSel]} =  this.inputFormGroup.value;
   const selection =  optionSel[inputSelected];
   this.dateTypeSelection = optionSel[inputSelected];
   const labelSelected = optionSelected.options.filter(ele=> ele.field === "subType" ? 
         this.getSelectedOptionFromLabel(event, ele, selection) :  ele.bindValue === selection)[0];
    const {searchButton} = this.selectedOption;
    this.setLinkedSearchOptionQuery(labelSelected, false);
    //this.selectedOption = {...this.getSelectionOptionList(optionSelected),...{searchButton}};
    this.inputGroupsLevelOptions = labelSelected;
    this.removeFormControl("inputGroupsLevel");
    this.createArrayFormControl('inputGroupsLevel',this.inputFormGroup);
    this.buildArrayElementControl(labelSelected.elements, this.inputFormGroup.get("inputGroupsLevel"));
    if(this.optionListSelected["dropDownOptionsFromApi"]) {
      const { methodName, optionLevel } = this.optionListSelected["dropDownOptionsFromApi"];
      this.setOptionsFromApi(methodName, optionLevel);
    } 
  }
  setLinkedSearchOptionQuery(optionSelected, isOptionChange) {
    const linkedOptions = this.inputSearchOptions.some(ele => ele.isLinkedObj);
    if(!linkedOptions) {
      return false;
    }
    optionSelected?.linkedTo ? this.pushLinkedValueToQuery(optionSelected) : this.clearQueryFrLinkedOptions(isOptionChange);
  }
  pushLinkedValueToQuery(optionSelected) {
    const linkedOption = this.inputSearchOptions.find(ele => ele.field === optionSelected.linkedTo), 
      index = this.inputSearchOptions.findIndex(ele => ele.field === optionSelected.linkedTo);
      if(linkedOption && index) {
        linkedOption.query = [];
        linkedOption.query.push(optionSelected.linkValue || optionSelected.defaultLinkValue);
        this.inputSearchOptions[index] = { ...linkedOption };
      }
  }
  clearQueryFrLinkedOptions(isOptionChange) {
    if(!isOptionChange) {
      return false;
    }
    const searchedChipObj = this.commonSearchService.inputSearchChip,
    linkedOptions = this.inputSearchOptions.filter(ele => ele.query?.length && ele.isLinkedObj);
    linkedOptions?.forEach((ele) => {
      ele.query = ele?.isLinkedObj && !searchedChipObj?.[ele?.linkedWith] ?  [] : ele.query;
    });
  }
  inputGroupsLevelSelected(){
   
  }
  createArrayFormControl(name,form){
    this.addFormControl(
      name,
      this.formBuilder.array([]),
      form
    );
    return this.inputGroups;
  }
  get inputGroups(){
    return this.inputFormGroup.controls.inputGroups as UntypedFormArray;
  }
  get inputGroupsLevel(){
    return this.inputFormGroup.controls.inputGroupsLevel as UntypedFormArray;
  }
  createFormObject(formControl){
    return formControl;
  }
  getSelectionOptionList(selectedOption){
    const { searchButton,placeholder,elements,field,defaultSelect, columnClass, linkedTo, defaultLinkValue,filterByUser} = selectedOption;
    const formControl = {
      placeholder,
      elements,
      field,
      searchButton,
      defaultSelect,
      columnClass,
      linkedTo,
      defaultLinkValue,
      filterByUser
   }
   return this.createFormObject(formControl);
  }
  getDefaultOptionList(){

  }
  resetFormControl(){

  }
  removeFormControl(field){
   this.inputFormGroup?.removeControl(field);
  }
  
  createFormControl(defaultValue){
    const group = {
      inputSelected:new UntypedFormControl(defaultValue),
      savedSearchName:new UntypedFormControl()
    };
    this.inputFormGroup =  new UntypedFormGroup(group);
  }
  trackByFn(index, item) {
    return item.field;
  }
}
