import { ComponentFixture, TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { BaseInputSearchComponent } from './base-input-search.component';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormControl, UntypedFormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { of, Subject, BehaviorSubject } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, Injector } from '@angular/core';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonService } from '@appServices/common/common.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { LoaderService } from '@appServices/common/loader.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CONSTANTS } from '@appConstants/constants';
import * as getControlByNameUtil from '@appUtilities/getControlByName';
import * as resetFormControlErrorUtil from '@appUtilities/resetFormControlError';
import * as updateTreeValidityUtil from '@appUtilities/updateValueValidatityForTree';

// Mock services
class MockBaseInputSearchService {
  inputOptions = [
    {
      field: 'mockFieldWithSubLevels',
      label: 'Mock Field With Sub Levels',
      defaultSelect: true,
      elements: [
        {
          field: 'mainSelect',
          type: 'select',
          options: [
            { label: 'Default Sub Option', bindValue: 'defaultSubVal', defaultSelect: true, elements: [{ field: 'subLevelElement', value: 'subDefault' }] }
          ]
        }
      ]
    },
    { field: 'periodWeekField', label: 'Period Week Field', defaultSelect: false, elements: [{ field: 'periodElement', value: '' }], dropDownOptionsFromApi: { methodName: 'getPeriodOptions', optionLevel: 'selectedOption' } },
    { field: 'featureFlaggedField', label: 'Feature Flagged Field', defaultSelect: false, featureFlag: 'testFeatureFlag', elements: [{ field: 'featureElement', value: '' }] },
    { field: 'linkedField', label: 'Linked Field', isLinkedObj: true, query: [], linkedWith: 'sourceField', elements: [] },
    { field: 'sourceField', label: 'Source Field', elements: [], linkedTo: 'linkedField', linkValue: 'linkedValue123' },
    { field: 'productGroupType', label: 'Product Group Type', defaultSelect: false, elements: [{ field: 'pgType', value: 'BASE' }] }
  ];
  inputFormGrpValue: any;
  setActiveCurrentSearchType = jasmine.createSpy('setActiveCurrentSearchType');
  getInputFieldSelected = jasmine.createSpy('getInputFieldSelected').and.returnValue({});
  setChipForField = jasmine.createSpy('setChipForField');
  generateQueryForOptions = jasmine.createSpy('generateQueryForOptions').and.returnValue(false);
  getActiveCurrentSearchType = jasmine.createSpy('getActiveCurrentSearchType').and.returnValue('testSearchType');
  getDataForInputSearch = jasmine.createSpy('getDataForInputSearch').and.returnValue(of({ data: 'searchData' }));
  setFormQuery = jasmine.createSpy('setFormQuery');
  formQuery = jasmine.createSpy('formQuery').and.returnValue('query');
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
  getLastPeriodOptions = jasmine.createSpy('getLastPeriodOptions').and.returnValue(of([{ periodWeek: '202501', periodId: 1 }, { periodWeek: '202502', periodId: 2 }]));
  postDataForInputSearch = jasmine.createSpy('postDataForInputSearch');
  setQueryWithOrFilter = jasmine.createSpy('setQueryWithOrFilter');
  queryWithOrFilter = 'queryWithOrFilter';
  inputSearchChip = function(val) { return {}; };
  currentRouter = 'mockRouter';
  mockRouter = new BehaviorSubject<any>(null);
}

class MockSearchUsersService {
  getUsers = jasmine.createSpy('getUsers').and.returnValue(of([{ firstName: 'John', lastName: 'Doe', userId: 'john.doe' }]));
}

class MockCommonSearchService {
  isFiltered = false;
  isStoreIdUserSearchEnabled = false;
  fetchDefaultOptions = jasmine.createSpy('fetchDefaultOptions');
  getFilterOption = jasmine.createSpy('getFilterOption').and.returnValue({});
  getInputSearchChip = jasmine.createSpy('getInputSearchChip').and.returnValue({});
  setInputSearchChip = jasmine.createSpy('setInputSearchChip');
  inputSearchChip = { sourceField: 'someValue' }; // For linked options test
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
}

class MockCommonService {
  getPeriodWeeks = jasmine.createSpy('getPeriodWeeks').and.returnValue(of(['202501', '202502']));
}

class MockCommonRouteService {
  currentActivatedRoute = 'mockRoute';
  currentRoute = 'mockRoute';
  debugFlagEnabledRoutes = [];
  router = { navigate: jasmine.createSpy('navigate') } as any;
  acivatedRoute = {};
  currentRouter = 'mockRoute';
  routerPage = '';
  isDebugTrueExists = false;
  isBpdReqPage = false;
  getCurrentRoute = jasmine.createSpy('getCurrentRoute').and.returnValue('mockRoute');
  getCurrentActivatedRoute = jasmine.createSpy('getCurrentActivatedRoute').and.returnValue('mockActivatedRoute');
  getCurrentRouteName = jasmine.createSpy('getCurrentRouteName');
  getCurrentRoutePath = jasmine.createSpy('getCurrentRoutePath');
  getCurrentRouteParams = jasmine.createSpy('getCurrentRouteParams');
}

class MockLoaderService {}
class MockBaseSavedSearchService {}
class MockFacetItemService {}
class MockFeatureFlagsService {
  isFeatureFlagEnabled = jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true);
}

fdescribe('BaseInputSearchComponent', () => {
  let component: BaseInputSearchComponent;
  let fixture: ComponentFixture<BaseInputSearchComponent>;
  let mockAppInjector: jasmine.SpyObj<Injector>;
  let mockBaseInputSearchService: MockBaseInputSearchService;
  let mockSearchUsersService: MockSearchUsersService;
  let mockCommonSearchService: MockCommonSearchService;
  let mockCommonService: MockCommonService;
  let mockCommonRouteService: MockCommonRouteService;
  let mockLoaderService: MockLoaderService;
  let mockBaseSavedSearchService: MockBaseSavedSearchService;
  let mockFacetItemService: MockFacetItemService;
  let mockFeatureFlagsService: MockFeatureFlagsService;
  let fb: UntypedFormBuilder;
  
  beforeEach(async () => {
    // Create fresh mock instances before each test
    mockBaseInputSearchService = new MockBaseInputSearchService();
    mockSearchUsersService = new MockSearchUsersService();
    mockCommonSearchService = new MockCommonSearchService();
    mockCommonService = new MockCommonService();
    mockCommonRouteService = new MockCommonRouteService();
    mockLoaderService = new MockLoaderService();
    mockBaseSavedSearchService = new MockBaseSavedSearchService();
    mockFacetItemService = new MockFacetItemService();
    mockFeatureFlagsService = new MockFeatureFlagsService();

    // Mock utility functions
    spyOn(getControlByNameUtil, 'getControlByName').and.callFake(({ rootControl, controlName }) => {
      if (rootControl?.get('inputGroups')) {
        const inputGroups = rootControl.get('inputGroups') as UntypedFormArray;
        for (let i = 0; i < inputGroups.length; i++) {
          const group = inputGroups.at(i);
          if (group.get(controlName)) {
            return [group.get(controlName)];
          }
        }
      }
      return null;
    });

    spyOn(resetFormControlErrorUtil, 'resetFormControlError').and.stub();
    spyOn(updateTreeValidityUtil, 'updateTreeValidity').and.stub();

    // Mock AppInjector
    mockAppInjector = jasmine.createSpyObj('Injector', ['get']);
    spyOn(AppInjector, 'getInjector').and.returnValue(mockAppInjector);

    mockAppInjector.get.and.callFake((token: any) => {
      if (token === UntypedFormBuilder) {
        return new UntypedFormBuilder(); // Return a real instance
      }
      if (token === BaseInputSearchService) return mockBaseInputSearchService;
      if (token === SearchUsersService) return mockSearchUsersService;
      if (token === CommonSearchService) return mockCommonSearchService;
      if (token === CommonService) return mockCommonService;
      if (token === CommonRouteService) return mockCommonRouteService;
      if (token === LoaderService) return mockLoaderService;
      if (token === BaseSavedSearchService) return mockBaseSavedSearchService;
      if (token === FacetItemService) return mockFacetItemService;
      if (token === FeatureFlagsService) return mockFeatureFlagsService;
      return undefined;
    });

    await TestBed.configureTestingModule({
      declarations: [BaseInputSearchComponent],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BaseInputSearchComponent);
    component = fixture.componentInstance;
    fb = TestBed.inject(UntypedFormBuilder);

    // Set @Input properties
    component.currentSearchType = 'testSearchType';
    component.isHideSavedSearch = false;

    // Initialize form for all tests to avoid null errors
    component.inputFormGroup = fb.group({
      inputSelected: [mockBaseInputSearchService.inputOptions[0].field],
      savedSearchName: [''],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });
  });

  // Helper to initialize component and run ngOnInit
  function initializeComponent() {
    // Manually call ngOnInit instead of using fixture.detectChanges()
    // to avoid potential issues with the fixture
    component.ngOnInit();
  }

  it('should create the component', () => {
    initializeComponent();
    expect(component).toBeTruthy();
  });

  // Test for the searchClickHandler with inputSearchChip function
  it('should properly call inputSearchChip during search', () => {
    // Setup the test
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    
    // Create a form with a value
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })])
    });
    
    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    
    // Mock out methods that will be called
    mockBaseInputSearchService.getInputFieldSelected.and.returnValue({field: 'testField', label: 'Test Field'});
    mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);
    
    // Create a spy for the inputSearchChip function
    const inputSearchChipSpy = jasmine.createSpy('inputSearchChip').and.returnValue({testField: {label: 'chipValue'}});
    mockBaseInputSearchService.inputSearchChip = inputSearchChipSpy;
    
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    // Execute
    component.searchClickHandler();
    
    // Verify
    expect(inputSearchChipSpy).toHaveBeenCalledWith(component.inputFormGroup.value);
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalledWith({testField: {label: 'chipValue'}});
  });

  // Test for the getUniqueListBy method
  it('should correctly filter duplicate objects in getUniqueListBy', () => {
    const testArray = [
      { id: 1, name: 'Item 1', periodWeek: '202501' },
      { id: 2, name: 'Item 2', periodWeek: '202502' },
      { id: 3, name: 'Item 3', periodWeek: '202501' }, // Duplicate periodWeek
      { id: 4, name: 'Item 4', periodWeek: '202503' }
    ];
    
    const result = component.getUniqueListBy(testArray, 'periodWeek');
    
    expect(result.length).toBe(3); // Should filter out duplicates
    expect(result.map(item => (item as any).periodWeek).sort()).toEqual(['202501', '202502', '202503'].sort());
  });
  
  // Test for empty array in getUniqueListBy
  it('should handle empty array in getUniqueListBy', () => {
    const result = component.getUniqueListBy([], 'anyKey');
    expect(result).toEqual([]);
  });
  
  // Test for getDataForInputSearch method
  it('should correctly handle getDataForInputSearch method', () => {
    const mockResponse = { data: 'test data' };
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService.currentRouter = 'testRouter';
    mockBaseInputSearchService['testRouter'] = new BehaviorSubject(null);
    
    component.getDataForInputSearch();
    
    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect(mockBaseInputSearchService['testRouter'].value).toEqual(mockResponse);
  });

  // Test for getPeriodOptions method
  it('should correctly handle getPeriodOptions method', () => {
    const mockPeriodDetails = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockPeriodDetails));
    spyOn(component, 'setOptions').and.callThrough();
    
    const selectedOption = { elements: [{ options: [{ defaultSelect: true }] }] };
    component.getPeriodOptions(selectedOption);
    
    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(selectedOption, mockPeriodDetails);
  });

  // Test for setOptions method
  it('should correctly handle setOptions method', () => {
    const mockPeriodDetails = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 },
      { periodWeek: '202501', periodId: 1 } // Duplicate to test getUniqueListBy
    ];
    
    const selectedOption = {
      elements: [{
        options: [{
          bindLabel: 'Default Option',
          bindValue: 'default',
          field: 'default',
          defaultSelect: true
        }]
      }]
    };
    
    component.setOptions(selectedOption, mockPeriodDetails);
    
    // Verify options were sorted and added with default option at beginning
    expect(selectedOption.elements[0].options.length).toBe(3); // Default + 2 unique period options
    expect(selectedOption.elements[0].options[0].defaultSelect).toBe(true);
    
    // Check sorting - should be in descending order by field (periodId) 
    // Convert to number for proper comparison since field is a number stored as string
    const nonDefaultOptions = selectedOption.elements[0].options.slice(1);
    expect(Number(nonDefaultOptions[0].field)).toBeGreaterThanOrEqual(Number(nonDefaultOptions[1].field));
  });

  // Test for setOptionsFromApi method
  it('should correctly handle setOptionsFromApi method', () => {
    const getPeriodOptionsSpy = spyOn(component, 'getPeriodOptions').and.callThrough();
    component.selectedOption = 'dummySelectedOption' as any;
    
    // Test with valid method and option level
    component.setOptionsFromApi('getPeriodOptions', 'selectedOption');
    expect(getPeriodOptionsSpy).toHaveBeenCalledWith('dummySelectedOption');
    
    // Test with invalid method
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi('nonExistentMethod', 'selectedOption');
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
    
    // Test with invalid option level
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi('getPeriodOptions', 'nonExistentOption');
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
    
    // Test with null inputs
    getPeriodOptionsSpy.calls.reset();
    component.setOptionsFromApi(null, null);
    expect(getPeriodOptionsSpy).not.toHaveBeenCalled();
  });

  // Test for specific scenario with the inputSearchChip implementation
  it('should handle inputSearchChip with selected element and showChip', () => {
    // Setup the BaseInputSearchService spy with a specific implementation
    const mockSelectedElement = {
      field: 'testField',
      label: 'Test Field',
      showChip: 'Test Chip Value'
    };
    
    mockBaseInputSearchService.getInputFieldSelected.and.returnValue(mockSelectedElement);
    
    // Create a form with proper structure for inputSearchChip
    const formValue = {
      inputSelected: 'testField',
      inputGroups: [
        { testField: 'Test Value' }
      ]
    };
    
    // Spy on the original implementation
    const originalInputSearchChip = mockBaseInputSearchService.inputSearchChip;
    
    // Create a more detailed spy
    mockBaseInputSearchService.inputSearchChip = function(inputFormGroup) {
      const selectedEle = mockSelectedElement;  // Using the mock directly
      // Should produce a chip object
      return { 
        [selectedEle.field]: { 
          [selectedEle.label]: selectedEle.showChip 
        } 
      };
    };
    
    spyOn(mockBaseInputSearchService, 'inputSearchChip').and.callThrough();
    
    // Create a form group for the test
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })])
    });
    
    // Set up the rest of the searchClickHandler pre-conditions
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();
    
    // Execute the search
    component.searchClickHandler();
    
    // Verify the inputSearchChip was called with the form value
    expect(mockBaseInputSearchService.inputSearchChip).toHaveBeenCalledWith(component.inputFormGroup.value);
    
    // Verify setInputSearchChip was called with the expected chip object
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalledWith({
      testField: { 'Test Field': 'Test Chip Value' }
    });
  });
});
