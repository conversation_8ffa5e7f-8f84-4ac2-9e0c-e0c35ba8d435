import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UntypedFormArray, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { REQUEST_CONSTANTS } from "@appModules/request/constants/request_constants";
import { AuthService } from "@appServices/common/auth.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { SearchUsersService } from "@appServices/common/search-users.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { dateInOriginalFormat } from "@appUtilities/date.utility";
import { loopThroughControls } from "@appUtilities/loopThroughControls";
import { nullCheckProperty } from "@appUtilities/nullCheck.utility";
import { scrollToErrorField } from "@appUtilities/scrollToError";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { updateTreeValidity } from "@appUtilities/updateValueValidatityForTree";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, Observable, Subject, forkJoin } from "rxjs";
import { map } from "rxjs/operators";
import { CommonRouteService } from "../../../shared/services/common/common-route.service";
import { FileAttachService } from "../../../shared/services/common/file-attach.service";
import { QueryGenerator } from "../../../shared/services/common/queryGenerator.service";
import { SearchOfferRequestService } from "./search-offer-request.service";

@Injectable({
  providedIn: "root",
})
export class RequestFormService extends UnsubscribeAdapter {
  requestForm: UntypedFormGroup;
  offerCreate_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_CREATE_API);
  requestSaveSubmit_API: string = this._initialDataService.getConfigUrls(REQUEST_CONSTANTS.OFFER_REQ_SAVE_SUBMIT_API);
  createAndSubmit_API: string = this._initialDataService.getConfigUrls(REQUEST_CONSTANTS.OFFER_REQ_CREATE_RE_SUBMIT_API);
  assignUser: string = this._initialDataService.getConfigUrls(CONSTANTS.ASSIGN_USER);
  unAssignUser: string = this._initialDataService.getConfigUrls(CONSTANTS.UNASSIGN_USER);
  offerRequest: any = {};
  storeGroupValuesToBeRestored; //when offer type is changed, the ealier selected values needs to be restored
  setDeptVal$: Subject<any> = new Subject();
  offerReqSource: BehaviorSubject<any> = new BehaviorSubject(this.offerRequest);
  currentOfferRequest = this.offerReqSource.asObservable();
  isDraftSaveAttempted = new BehaviorSubject(false);
  isReqSubmitAttempted$ = new BehaviorSubject(false);
  setReqData$ = new BehaviorSubject(false);
  requestStatus$ = new Subject();
  assignedModal$ = new BehaviorSubject(false);
  offersData$ = new Subject();
  displayPgError$ = new Subject();
  totalAmtForMealDeal$ = new Subject<any>();
  selectedChannelForTier$ = new BehaviorSubject(false);
  selectedOfferLimitTypeForTier$ = new BehaviorSubject(false);
  passClonedObject$ = new BehaviorSubject(false); // to clone OR passing instance
  cloningProcess$ = new BehaviorSubject(false); //Once copy is clicked, and before route change
  dontsaveVal$ = new BehaviorSubject(false);
  adTypeObsrvble$ = new Subject();
  startDateObsrvble$ = new BehaviorSubject(null);
  endDateObsrvble$ = new BehaviorSubject(null);
  copyingVar: boolean = false;
  selectedOfferLimitType = null;
  selectedChannelForTier = null;
  cachedDigitalOfferStatus = {};
  cachedNonDigitalOfferStatus = {};
  formattedCreatedDate;
  differenceDate;
  offerEffectiveStartDate;
  isJustificationBoolean = new BehaviorSubject(false);
  isEditNotificatonBoolean = new BehaviorSubject(false);
  isUpdateNotificationBoolean = new BehaviorSubject(false);
  isPreviousDGStatusUpdating$ = new BehaviorSubject(null);
  isPreviousNDStatusUpdating$ = new BehaviorSubject(null);
  isRemovedUnclippedOnBoolean = new BehaviorSubject(false);
  isofferRequestRemovedBoolean = new BehaviorSubject(false);
  hideApiErrorOnCreateRequest$ = new Subject();
  setCategoryValue$ = new Subject(); //category in POD section to be derived when a "Buy" Product Group is selected
  isSavedFromNavigationOverlay = false; // If saved from the navigation overlay
  requestDigitalStatus$ = new Subject();
  requestNonDigitalStatus$ = new Subject();
  digitalStatus: string = null;
  nonDigitalStatus: string = null;
  callingFrom: any;
  offerRequestBaseService;

  populateStoreGroupFields$ = new Subject();
  isCallSetAttachmentData$ = new Subject();
  selectedOfferType$ = new Subject();
  selectedOfferLimitType$ = new Subject();
  requestData$ = new BehaviorSubject(null);
  reqId$ = new BehaviorSubject(null);
  offerRequestDataDisplay$ = new BehaviorSubject(null);
  offerNopaGroupDataDisplay$ = new BehaviorSubject(null);
  offeradditionalDescriptionGroupDataDisplay$ = new BehaviorSubject(null);
  offerJustificationGroupDataDisplay$ = new BehaviorSubject(null);
  requestEditUpdateData$ = new BehaviorSubject(null);
  offerRuleDay$ = new BehaviorSubject(null);
  offerRuleTime$ = new BehaviorSubject(null);
  offerRuleTimeValidation$ = new Subject();

  digitalId: any;
  nonDigitalId: any;
  digitalUserDetails: any;
  nonDigitalUserDetails: any;
  makeSearchCall: any;

  displaySubmitBtn$ = new Subject();
  displayUpdateBtn$ = new Subject();
  selectedChannel$ = new BehaviorSubject(null);
  createdApplicationId$ = new BehaviorSubject(CONSTANTS.OMS);
  isNonImpactedFieldsChanged: boolean = true;
  isOfferSaved: boolean;
  onChangeChannel$ = new Subject();

  compRouter: any;
  compRoute: any;

  appData;

  requestData;

  isValidOfferExists = false;

  tabsModelObj = {};
  reqId: any;
  lastUpdatedTs: any;
  selectedDeliveryChannel;
  hasSubmitted;
  channels: any;
  digitalEditStatus;
  nonDigitalEditStatus;
  filesList = new BehaviorSubject([]);
  removeFilesList = new BehaviorSubject([]);
  attachedFilesList: any = [];
  uploadedFilesList: any = [];
  fileUploadBool = new BehaviorSubject(false);
  public userNonDigital$ = new Subject();
  public userdDigital$ = new Subject();
  public changeReasonData$ = new BehaviorSubject({});
  setReqSectionValidationsForSubmit;
  setJustificationValidationsForSubmit;
  requestDigitalStatus: any;
  requestNonDigitalStatus: any;
  createdApplicationId: any = CONSTANTS.OMS;
  createdTs: any;
  createdUserId: any;
  requestStatus = null;
  isReqSubmitted: boolean; // if Request is being attempted to submit / submitted, then holds true

  isRedirectRoute$ = new Subject();
  onRouteChange$ = new Subject();
  isReqSaved$ = new Subject();
  isDisplayNavigationWarning = true;
  isOfferFormValidForSaveFlag: boolean; // Tracks if the offer form is valid for save
  showTotalAmountForAllDiscounts$ = new BehaviorSubject({});
  getCategoryData$ = new Subject();

  warningMsg: string;
  saveAnywayPLU: boolean = false;
  showPLUWraningTmpl: boolean = false;
  updateOffer: boolean = false;
  isOfferTypeDetailsFormChanged: boolean = false;
  checkBoxCtrlsKeys = ["delivery", "dug", "inStorePurchase", "wug"];
  nonImpactedFields = [
    "department",
    "customerSegment",
    "nopaNumbers",
    "nopaStartDate",
    "nopaEndDate",
    "isBilled",
    "billingOptions",
    "desc",
  ];
  impactedFields = [
    "brandAndSize",
    "group",
    "groupDivision",
    "offerEffectiveStartDate",
    "offerEffectiveEndDate",
    "fulfillmentChannel",
    "ecommPromoCode",
    "isAutoApplyPromoCode",
    "firstTimeCustomerOnly",
    "orderCount",
    "notCombinableWith",
    "behavioralAction",
    "validWithOtherOffer",
    "order",
    "ecommPromoType",
    "usageLimitTypePerUser",
    "isGiftCard",
    "endHr",
    "endMin",
    "endPeriod",
    "startHr",
    "startMin",
    "startPeriod",
    "friday",
    "monday",
    "saturday",
    "sunday",
    "thursday",
    "tuesday",
    "wednesday",
    "offerFlag",
    "initialSubscriptionOffer",
    "cpg",
    "isRedeemableInSameTransaction",
    "noOfTransactions",
    "minimumSpend",
    "minimumSpendUom"
  ];
  requestDataPayload: any;
  allOffersAlongWithOfferRequestUpdate: boolean;
  onlyPluBarCodeChanged: any;
  otherFieldsChanged: boolean;
  dynamicComponent: any = [];
  uppNonEditableFields:any = ["productgroup.name",
  "discounts.name",
  "discounts.amount",
  "quantityUnitType",
  "itemLimit",
  "dollarLimit",
  "weightLimit",
  "nonDigitalRedemptionStoreGroupIds",
  "nonDigitalRedemptionStoreGroupNames",
  "digitalRedemptionStoreGroupIds",
  "digitalRedemptionStoreGroupNames",
  "podStoreGroupIds",
  "podStoreGroupNames"];
  constructor(
    private _toastr: ToastrService,
    private _initialDataService: InitialDataService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private authService: AuthService,
    private _http: HttpClient,
    private _fileAttachService: FileAttachService,
    private _queryGenerator: QueryGenerator,
    private _searchUsersService: SearchUsersService,
    public generalOfferTypeService: GeneralOfferTypeService,
    public commonRouteService: CommonRouteService,
    public featureFlagsService: FeatureFlagsService,
    private commonSearchService: CommonSearchService
  ) { 
    super();
    this.initSubscribes();
    this.initVariables();
    this.generalOfferTypeService.requestFormService = this;
  }

  getChangeReasonData(): Observable<any> {
    return this.changeReasonData$.asObservable();
  }

  initSubscribes() {
    // Get the offer for the above API
    this.subs.sink = this._searchOfferRequestService.currentOfferRequestsCreateObvl.subscribe((response) => {
      if (response) {
        this.setSearchResponse();
      }
    });

    this.subs.sink = this.requestData$.subscribe((data) => {
      if (!data) {
        return false;
      }

      const {
        info: { digitalStatus, nonDigitalStatus },
      } = data;
      this.isReqSubmitted = true;
      this.digitalStatus = digitalStatus;
      this.nonDigitalStatus = nonDigitalStatus;
      if ((!digitalStatus && !nonDigitalStatus) || digitalStatus == "I" || nonDigitalStatus == "I") {
        this.isReqSubmitted = false;
      }
    });

    this.subs.sink =  this.createdApplicationId$.subscribe((obj)=>{
      this.createdApplicationId = obj;
    });
  }
  createRequestDataInstance() {
    this.requestData$ = new BehaviorSubject(null);
  }

  hideApiErrorOnRequestMain() {
    return this.hideApiErrorOnCreateRequest$.asObservable();
  }
  initVariables() {
    this.appData = this._initialDataService.getAppData();
    this.channels = this.appData.offerDeliveryChannels;
    this.generalOfferTypeService.getValueByKey = this.getValueByKey.bind(this);
  }



    disableUPPFormControls(formFields,createdApplicationId,nonEditableFileds,section="") {
      // If UPP feature is not enabled, return formFields as it is
      if (!this.featureFlagsService.isuppEnabled || createdApplicationId === "OMS" || formFields === null || !formFields) {
          return;
      }
      if(nonEditableFileds && nonEditableFileds.length > 0){
        this.uppNonEditableFields = nonEditableFileds;
      }
      // Get list of UPP fields to disable
      let controlsToDisable = [];
      if(section === "")
        controlsToDisable = this.uppNonEditableFields.filter(field => field.indexOf('.') === -1);
      else
        controlsToDisable = this.uppNonEditableFields.filter(field => field.includes(section))?.map(item => item.split(".")[1]);

      // Disable UPP fields in formFields
      Object.keys(formFields).forEach(key => {
        const control = formFields[key];

        if (control instanceof UntypedFormControl) {
            if (controlsToDisable.includes(key)) {
                control.disable({onlySelf: true});
            }
        }else if (control instanceof UntypedFormGroup || control instanceof UntypedFormArray) {
          this.disableUPPFormControls(control.controls,createdApplicationId,nonEditableFileds,section);
      }
    });
  }

  setSearchResponse() {}
  getUsers(term) {
    let splitText = term && term.split(" ");
    if (splitText && splitText.length > 1) {
      splitText.pop();
      term = splitText.join(" ");
    }

    return this._searchUsersService.getUsers(term);
  }

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString(),
    };
  }
  public assignUserToOfferReq(digi, nonDigi, reqId) {
    let assignUsers;
    if (digi && nonDigi) {
      assignUsers = [
        {
          userId: digi,
          userType: "DG",
        },
        {
          userId: nonDigi,
          userType: "ND",
        },
      ];
    }
    if (digi && !nonDigi) {
      assignUsers = [
        {
          userId: digi,
          userType: "DG",
        },
      ];
    }
    if (nonDigi && !digi) {
      assignUsers = [
        {
          userId: nonDigi,
          userType: "ND",
        },
      ];
    }
    let searchInput = {
      reqObj: { headers: this.getHeaders() },
      offerRequestId: reqId,
      assignUsers,
    };
    return this._http.put(this.assignUser, searchInput);
  }
  public unAssignUserToOfferReq(digital, nonDigital, reqId) {
    let unAssignUserTypes = [];
    if (digital && nonDigital) {
      unAssignUserTypes = [digital, nonDigital];
    }
    if (digital) {
      unAssignUserTypes = [digital];
    }
    if (nonDigital) {
      unAssignUserTypes = [nonDigital];
    }
    let searchInput = {
      reqObj: { headers: this.getHeaders() },
      offerRequestId: reqId,
      unAssignUserTypes,
    };
    return this._http.put(this.unAssignUser, searchInput);
  }

  isUpdateOffer() {
    if (!this.isOfferTypeDetailsFormChanged) {
      this.getORFormFields(this.requestForm);
      if (this.isNonImpactedFieldsChanged) {
        this.getORFormFields(this.generalOfferTypeService.generalOfferTypeForm);
      }
      this.updateOffer = !this.isNonImpactedFieldsChanged;
    } else {
      this.updateOffer = this.isOfferTypeDetailsFormChanged;
    }
  }

  getORFormFields(group) {
    Object.keys(group.controls).forEach((key) => {
      const control = group.get(key);
      if (control instanceof UntypedFormGroup || control instanceof UntypedFormArray) {
        if (this.isNonImpactedFieldsChanged) {
          this.getORFormFields(control);
        }
      } else if (control instanceof UntypedFormControl) {
        if (control.touched && control.dirty && key === "pluTriggerBarcode") {
          this.onlyPluBarCodeChanged = true;
        }
        if ((this.checkBoxCtrlsKeys.includes(key) || control.touched) && control.dirty && this.isNonImpactedFieldsChanged) {
          this.isNonImpactedFieldsChanged = this.nonImpactedFields.includes(key);
          if (!this.isNonImpactedFieldsChanged) {
            this.isNonImpactedFieldsChanged = false;
          }
          if (this.impactedFields.includes(key)) {
            this.otherFieldsChanged = true;
          }
        }
      }
    });
  }
  saveOR() {
    this.callingFrom = "save";
    // Triggers when saving the OR  in the unsubmitted/sumbmitted state
    let validationMethod = this.isFormValidOnSave.bind(this);

    // If in intial status, run validations which were required on save, else submit validations
    if (this.isReqSubmitted) {
      validationMethod = this.isFormValidPostSave.bind(this);
    }
    if (!validationMethod({})) {
      return;
    }
    // map form to  Request Object
    this.mapFormDataToReqObj();
    this.authService.onUserDataAvailable(this.saveOfferRequest.bind(this));
  }

  saveSubmitOR(submitted) {
    this.callingFrom = "submit";

    this.copyingVar = false;
    this.showPLUWraningTmpl = false;
    // Triggers when submitting the offer
    if (!this.isFormValidPostSave({ isSubmitAttempted: true })) {
      return false;
    }

    // map form to  Request Object
    this.mapFormDataToReqObj();
    this.authService.onUserDataAvailable(this.saveAndSubmitOfferRequest.bind(this, submitted));
    // this.assignUser();
  }

  saveAndSubmitOfferRequest(submitted) {
    const reqBody: any = this.requestData;
    // removing the assingment as we should not pass for save calls
    reqBody.info.digitalUser = null;
    reqBody.info.nonDigitalUser = null;
    if ( this.saveAnywayPLU) {
      reqBody.info["validatePluTriggerBarcode"] = false;
    }
    let searchInput = { ...reqBody, updateOffers: true, reqObj: { headers: this.getHeaders() } };
    if(!this.checkFulfillmentChannelEnabled(reqBody?.info?.programCode,reqBody?.info?.deliveryChannel)){
      if(reqBody?.info?.fulfillmentChannel){
        reqBody.info.fulfillmentChannel=null;
      }
    }
    if (this.requestData.info.id) {
      return this._http.put(this.requestSaveSubmit_API, searchInput).subscribe(
        (response) => this.postSave(response, "submitted"),
        (err) => {
        
            this.handleWarnings(err.error);
          
        }
      );
    } else {
      return this._http.post(this.createAndSubmit_API, searchInput).subscribe(
        (response) => this.postSave(response, "created", submitted),
        (err) => {
          
            this.handleWarnings(err.error);
          
        }
      );
    }
  }

  resetSubmitValidations() {
    // Fix: Clearing validations required on submit if the submit is unsuccessful(invalid form) and user is attempting to  save(On save, submit validations shouldnt be executed)
    const arr = ["pluTriggerBarcode", "adType", "offerEffectiveStartDate", "offerEffectiveEndDate", "usageLimitTypePerUser"],
      offerReqGroup = this.requestForm.get("offerReqGroup");
    arr.forEach((control) => {
      offerReqGroup.get(control).clearValidators();
    });
    updateTreeValidity(offerReqGroup as UntypedFormGroup);
    this.isReqSubmitAttempted$.next(false);
  }
  isValidRewardAcumulationRule() {
    let usageLimitTypePerUser = this.requestForm.get("offerReqGroup").value["usageLimitTypePerUser"];
    let generalInformationForm = this.generalOfferTypeService.generalOfferTypeForm.value.generalInformationForm,
      selectedOfferType = this.generalOfferTypeService.getKeyByValue(null, generalInformationForm.type);
    if (selectedOfferType && selectedOfferType === "REWARDS_ACCUMULATION" && usageLimitTypePerUser != "UNLIMITED") {
      return false;
    } else {
      return true;
    }
  }
  isValidEnterPriseInstantWinRule() {
    let usageLimitTypePerUser = this.requestForm.get("offerReqGroup").value["usageLimitTypePerUser"];
    let generalInformationForm = this.generalOfferTypeService.generalOfferTypeForm.value.generalInformationForm,
      selectedOfferType = this.generalOfferTypeService.getKeyByValue(null, generalInformationForm.type);
    if (selectedOfferType && selectedOfferType === "INSTANT_WIN" && usageLimitTypePerUser != "LIMITED") {
      return false;
    } else {
      return true;
    }
  }
  get isBehavioralActionEnabled() {
    return this.featureFlagsService.isFeatureFlagEnabled("enableBehavioralOffers");
  }
  
  isVersionsForItemBasketEnabled()
  {
    const pCode = this.generalOfferTypeService.facetItemService.programCodeSelected;
    return (this.featureFlagsService.isFeatureFlagEnabled("enableEmployeeOffers") && [CONSTANTS.SPD].includes(pCode));
  }

  isValidTimeRule() {
    let isValid = true;

    let validation = {
      valid: isValid,
      startMsg: "",
      endMsg: "",
    };
    // time validation if present on the form
    const formGroup = this.requestForm.get("offerRuleTimeGroup");
    if (formGroup) {
      // nothing entered
      if (
        formGroup.get("startHr").value == null &&
        formGroup.get("startMin").value == null &&
        formGroup.get("startPeriod").value == null &&
        formGroup.get("endHr").value == null &&
        formGroup.get("endMin").value == null &&
        formGroup.get("endPeriod").value == null
      ) {
        return isValid;
      }

      if (formGroup.get("startHr").value == null || formGroup.get("startMin").value == null || formGroup.get("startPeriod").value == null) {
        isValid = false;
        validation.valid = isValid;
        validation.startMsg = "Please enter a valid start time.";
        this.offerRuleTimeValidation$.next(validation);
      }

      if (formGroup.get("endHr").value == null || formGroup.get("endMin").value == null || formGroup.get("endPeriod").value == null) {
        isValid = false;
        validation.valid = isValid;
        validation.endMsg = "Please enter a valid end time.";
        this.offerRuleTimeValidation$.next(validation);
      }

      if (!isValid) {
        return isValid;
      }

      formGroup.get("startMin").setValue(("0" + formGroup.get("startMin").value).slice(-2));
      formGroup.get("endMin").setValue(("0" + formGroup.get("endMin").value).slice(-2));
      formGroup
        .get("start")
        .setValue(formGroup.get("startHr").value + ":" + formGroup.get("startMin").value + " " + formGroup.get("startPeriod").value);
      formGroup
        .get("end")
        .setValue(formGroup.get("endHr").value + ":" + formGroup.get("endMin").value + " " + formGroup.get("endPeriod").value);

      // convert to military time
      let sth =
        parseInt(formGroup.get("startHr").value, 10) +
        (formGroup.get("startPeriod").value === "PM" && parseInt(formGroup.get("startHr").value, 10) !== 12 ? 12 : 0);
      let edh =
        parseInt(formGroup.get("endHr").value, 10) +
        (formGroup.get("endPeriod").value === "PM" && parseInt(formGroup.get("endHr").value, 10) !== 12 ? 12 : 0);

      if (sth == 12 && formGroup.get("startPeriod").value == "AM") {
        sth = 0;
      }
      if (edh == 12 && formGroup.get("endPeriod").value == "AM") {
        edh = 0;
      }

      if (sth === edh) {
        let stm = parseInt(formGroup.get("startMin").value);
        let edm = parseInt(formGroup.get("endMin").value);

        if (stm === edm) {
          isValid = false;
          validation.valid = isValid;
          validation.endMsg = "Start time and end time should not be same.";
          this.offerRuleTimeValidation$.next(validation);
          return isValid;
        } else {
          if (stm > edm) {
            isValid = false;
            validation.valid = isValid;
            validation.startMsg = "Start time should be earlier than end time.";
            this.offerRuleTimeValidation$.next(validation);
            return isValid;
          }
        }
      } else {
        // hours are not equal

        if (sth > edh) {
          isValid = false;
          validation.valid = isValid;
          validation.startMsg = "Start time should be earlier than end time.";
        }
      }
    }

    this.offerRuleTimeValidation$.next(validation);
    return isValid;
  }

  isOfferFormValidForSave(obj) {
    // If data is entered, then validation needs to happen on save as well
    let { control } = obj;
    if (control.errors && !control.errors.required) {
      this.isOfferFormValidForSaveFlag = false;
    }
  }

  isFormValidOnSave() {
    // On save attempt, check if form is valid. If not, doesnt make the API calls
    let isFormValid = true;
    let offerType = this.generalOfferTypeService.generalOfferTypeForm.get("generalInformationForm").value["type"];
    let channel = this.requestForm.get("offerReqGroup").value["deliveryChannel"];
    isFormValid = this.isValidTimeRule();

    this.notifySaveAttempt();
    this.resetSubmitValidations();

    const obj = {
      rootControl: this.generalOfferTypeService.generalOfferTypeForm.get("offerRequestOffers"),
      callback: this.isOfferFormValidForSave.bind(this),
    };
    this.isOfferFormValidForSaveFlag = true;
    loopThroughControls(obj);

    if (
      !this.requestForm.valid ||
      !this.isOfferFormValidForSaveFlag ||
      (offerType === "Fab 5 / Score 4" && channel !== "IS") ||
      (offerType === "Store Closure" && channel !== "IS") ||
      !this.isValidEnterPriseInstantWinRule() ||
      !this.isValidRewardAcumulationRule()
    ) {
      setTimeout(() => {
        scrollToErrorField();
      }, 0);
      isFormValid = false;
    }
    return isFormValid;
  }

  updateAllOffersAlongWithOfferRequestUpdate() {
    const offerRequestOffers = this.requestData.rules.qualificationAndBenefit.offerRequestOffers,
      offerRequestData = JSON.parse(this.generalOfferTypeService.cloneOfferRequestData);
    const [offerRequestOffersElement] = offerRequestOffers,
      [offerRequestOffersElementData] = offerRequestData;
      
    const {
      storeGroupVersion: {
        productGroupVersions: [
          {
            discountVersion: {
              discounts: [{ chargebackDepartment: chargebackDepartmentUpdated }],
            },
          },
        ],
      },
    } = offerRequestOffersElement;

    const {
      storeGroupVersion: {
        productGroupVersions: [
          {
            discountVersion: {
              discounts: [{ chargebackDepartment: chargebackDepartmentOriginal }],
            },
          },
        ],
      },
    } = offerRequestOffersElementData;
    if (chargebackDepartmentUpdated !== chargebackDepartmentOriginal) {
      this.allOffersAlongWithOfferRequestUpdate = true;
    }
  }
  saveOfferRequest() {
    // Triggers when saving the OR  in the unsubmitted/sumbmitted state.
    const reqBody: any = this.requestData;
    
    if (reqBody.info.id) {
      this.setDetectChanges(this.requestData.rules.qualificationAndBenefit.offerRequestOffers);
    }
    // removing the assingment as we should not pass for save calls
    this.showPLUWraningTmpl = false;
    if (this.saveAnywayPLU) {
      reqBody.info["validatePluTriggerBarcode"] = false;
    }

    reqBody.info.digitalUser = null;
    reqBody.info.nonDigitalUser = null;
    if (this.otherFieldsChanged) {
      this.onlyPluBarCodeChanged = false;
    }
    if (this.otherFieldsChanged && !this.onlyPluBarCodeChanged) {
      this.allOffersAlongWithOfferRequestUpdate = true;
    }
    const generalInformationForm = this.generalOfferTypeService.generalOfferTypeForm.value.generalInformationForm;
    const offerRequestType = this.generalOfferTypeService.getKeyByValue(null, generalInformationForm.type);
    if ((offerRequestType === "WOD_OR_POD" || offerRequestType === "DEPARTMENT") && this.generalOfferTypeService?.cloneOfferRequestData) {
      this.updateAllOffersAlongWithOfferRequestUpdate();
    }
    if(!this.checkFulfillmentChannelEnabled(reqBody?.info?.programCode,reqBody?.info?.deliveryChannel)){
      if(reqBody?.info?.fulfillmentChannel){
        reqBody.info.fulfillmentChannel=null;
      }
    }

    if (this.requestData.info.id) {
      let searchInput = {
        ...reqBody,
        updateOffers: this.updateOffer,
        updateOnlyPluTriggerBarcode: this.onlyPluBarCodeChanged,
        allOffersAlongWithOfferRequestUpdate: this.allOffersAlongWithOfferRequestUpdate,
        cachedDigitalOfferStatus: this.cachedDigitalOfferStatus,
        cachedNonDigitalOfferStatus: this.cachedNonDigitalOfferStatus,
        reqObj: { headers: this.getHeaders() },
      };
      return this._http.put(this.offerCreate_API, searchInput).subscribe(
        (response) => {
          if (this.saveAnywayPLU) {
            this._toastr.success("Saved without PLU reservation", "", {
              timeOut: 3000,
              closeButton: true,
            });
          }
          this.postSave(response, "updated");
          this.isReqSaved$.next(true);
        },
        (err) => {
         
            this.handleWarnings(err.error);
          
        }
      );
    } else {
      let searchInput = { ...reqBody, updateOffers: true, reqObj: { headers: this.getHeaders() } };
      return this._http.post(this.offerCreate_API, searchInput).subscribe(
        (response) => {
          if (this.saveAnywayPLU) {
            this._toastr.success("Saved without PLU reservation", "", {
              timeOut: 3000,
              closeButton: true,
            });
          }
          this.postSave(response, "created");
          this.isReqSaved$.next(true);
          //If Saving Copied Expired OR, OR is not Expired any more
          this.commonSearchService.isShowExpiredInQuery = false;
        },
        (err) => {
         
            this.handleWarnings(err.error);
          
        }
      );
    }
  }
  handleWarnings(err) {
    this.showPLUWraningTmpl = false;
    this.warningMsg = "";
    let warningMsgArr = err.errors && err.errors.filter((value) => value.includes("warning"));
    if (warningMsgArr && warningMsgArr.length) {
      this.warningMsg = warningMsgArr.join("").split(":")[1] ? warningMsgArr.join("").split(":")[1] : "";
      if (this.warningMsg) {
        this.showPLUWraningTmpl = true;
        scrollToErrorField();
      }
    }
  }

  notifySaveAttempt() {
    this.requestForm.markAsUntouched();
    this.generalOfferTypeService.generalOfferTypeForm.markAsUntouched();
    this.isDraftSaveAttempted.next(true);
    updateTreeValidity(this.requestForm.get("offerReqGroup") as UntypedFormGroup);
  }

  notifySubmitAttempt() {
    this.notifySaveAttempt();
    this.generalOfferTypeService.generalOfferTypeForm.markAsUntouched();
    this.isReqSubmitAttempted$.next(true);
    this.setReqSectionValidationsForSubmit();
    this.setJustificationValidationsForSubmit();
  }

  isFormValidPostSave(obj = { isSubmitAttempted: false }) {
    // On submit/post submit API calls, check if form is valid. If not, doesnt make the API calls
    let isFormValid = true;
    let offerType = this.generalOfferTypeService.generalOfferTypeForm.get("generalInformationForm").value["type"];
    let channel = this.requestForm.get("offerReqGroup") && this.requestForm.get("offerReqGroup").value["deliveryChannel"];
    if (!this.isReqSubmitted && !obj.isSubmitAttempted) {
      return true;
    }

    isFormValid = this.isValidTimeRule();
    this.notifySubmitAttempt();

    if (
      !this.isValidEnterPriseInstantWinRule() ||
      !this.isValidRewardAcumulationRule() ||
      !this.requestForm.valid ||
      !this.generalOfferTypeService.generalOfferTypeForm.valid ||
      (offerType === "Store Closure" && channel !== "IS")
    ) {
      // Fix:Date field validation was taking longer, so holding until they were validated
      setTimeout(() => {
        scrollToErrorField();
      }, 0);

      isFormValid = false;
   
    }
    return isFormValid;
  }

  getReqId(obj, qKeyName) {
    // Todo: check if the existing functionality is working
    let value;
    Object.keys(obj).forEach(function eachKey(key) {
      return obj[key] === qKeyName ? (value = key) : null;
    });
    return value;
  }
  setUpdateNotification(digitalEditStatus, nonDigitalEditStatus) {
    if (digitalEditStatus && digitalEditStatus.editStatus === "U" && nonDigitalEditStatus && nonDigitalEditStatus.editStatus === "U") {
      this.isUpdateNotificationBoolean.next(true);
    }
  }
  postSave(requestData, keyName, submitted = false) {
    // Mark the forms as pristine post save
    this.requestForm.markAsPristine();
    this.generalOfferTypeService.generalOfferTypeForm.markAsPristine();
    this.generalOfferTypeService.addNewRow$.next("");
    this.isDisplayNavigationWarning = false;
    this.allOffersAlongWithOfferRequestUpdate = false;
    this.updateOffer = false;
    this.isOfferTypeDetailsFormChanged = false;
    this.otherFieldsChanged = false;
    this.isNonImpactedFieldsChanged = true;
    let id;
    if (keyName === "submitted") {
      id = this.getReqId(requestData, "updated");
    } else {
      id = this.getReqId(requestData, keyName);
    }

    const successMessage = keyName === "submitted" || submitted ? "Offer request submitted" : "Offer request saved";

    this.offerRuleDay$.next(this.requestData.rules.qualificationAndBenefit.day);
    this.offerRuleTime$.next(this.requestData.rules.qualificationAndBenefit.time);
    //If Saving Copied Expired OR, OR is not Expired any more
    this.commonSearchService.isShowExpiredInQuery = false;
    if (this.filesList.getValue().length > 0) {
      const requests = this.uploadFiles(id, this.filesList.getValue());
      this.filesList.next([]);
      requests.subscribe(
        (data) => {
          for (let file of data) {
            let fileData: any;
            if (file["url"] && file["url"].length > 0) {
              fileData = file;
              fileData.uploadStatus = true;
              fileData.id = id;
              // check the null or undefined for the attached file list
              if (!this.attachedFilesList) {
                this.attachedFilesList = [];
              }

              this.attachedFilesList.push(fileData);
              // //this.offerRequestId = id;
              this.uploadedFilesList = [];
              this.fileUploadBool.next(true);
            }
          }
          this.navigateToSummary(successMessage, id);
        },
        (err) => console.error(`${err}`)
      );
    } else {
      this.navigateToSummary(successMessage, id);
    }
  }
  navigateToSummary(successMessage, id) {
    // get the updated request details
    this.getReqDetails();

    let summaryPath = ROUTES_CONST.REQUEST.Summary,
      programCodeSelected = this.generalOfferTypeService.facetItemService.programCodeSelected;

    if (programCodeSelected !== CONSTANTS.SC) {
      summaryPath = `${programCodeSelected.toLocaleLowerCase()}-${ROUTES_CONST.REQUEST.Summary}`;
    }

    // If saved from the navigation overlay, it should redirect to the user selected path instead of summary pg
    !this.isSavedFromNavigationOverlay &&
      this.compRouter.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${summaryPath}/${id}`);
    // reset
    this.isSavedFromNavigationOverlay = false;
    this.isRedirectRoute$.next(true);

    this._toastr.success(successMessage, "", {
      timeOut: 3000,
      closeButton: true,
    });
  }

  compareStoreGroups(array1, array2) {
    if (array1.length !== array2.length) {
      return true;
    }
    this.Sort(array1);
    this.Sort(array2);
    const result = array1.filter(function (val, index) {
      return array2[index] !== val;
    });
    return result.length > 0;
  }

  Sort(array) {
    array.sort((value1, value2) => {
      return value1 - value2;
    });
  }

  uploadFiles(id, files) {
    let observableBatch = [];
    for (let file of files) {
      if (file) {
        observableBatch.push(this._fileAttachService.uploadFile(file, id).pipe(map((res) => res)));
      }
    }
    return forkJoin(observableBatch);
  }

  getReqDetails() {
    // If it is a edit page, set the form fields with values
    const reqId = this.compRoute.snapshot.paramMap.get("requestId");

    // Make service call only when there is reqId and if the data is not already available
    if (!reqId) {
      return;
    }

    // Makes API call to fetch the offer by request Id
    let paramsList = [
      {
        remove: false,
        parameter: "requestId",
        value: reqId,
      },
    ];
    this._queryGenerator.setQuery("");
    this._queryGenerator.pushParameters({ paramsList });
    this.authService.onUserDataAvailable(this.reqDetailsApi.bind(this));
  }

  reqDetailsApi() {
    this.subs.sink = this._searchOfferRequestService
      .searchOfferRequest(this._queryGenerator.getQuery(), false)
      .subscribe((response: any) => {
        // If there are no offers
        if (!response.offerRequests[0]) {
          this._toastr.warning("The offer Id might be invalid", "", {
            timeOut: 3000,
            closeButton: true,
          });
          return false;
        }
        this.requestDataPayload = response;
        this.generalOfferTypeService.cloneOfferRequestData = JSON.stringify(
          response.offerRequests[0].rules.qualificationAndBenefit.offerRequestOffers
        );
        this.digitalStatus = response.offerRequests[0].info.digitalStatus;
        this.nonDigitalStatus = response.offerRequests[0].info.nonDigitalStatus;
        let {
          rules: { qualificationAndBenefit },
        } = response.offerRequests[0];
        const pCode = this.generalOfferTypeService.facetItemService.programCodeSelected;
        if ([CONSTANTS.GR, CONSTANTS.SPD].includes(pCode) || this.commonRouteService.isBpdReqPage) {
          qualificationAndBenefit.offerRequestOffers =
            qualificationAndBenefit[`${this.generalOfferTypeService.facetItemService.reqOffersObjkey}OfferRequestOffers`];
        }
        this.requestData$.next(response.offerRequests[0]);
        this.requestDigitalStatus$.next(response.offerRequests[0].info.digitalStatus);
        this.requestNonDigitalStatus$.next(response.offerRequests[0].info.nonDigitalStatus);
        this._searchOfferRequestService.setOfferDetailsReqToObsvbl(response);
      });
  }

  getCreatePathFromPC() {
    //TO DO: Handle Program Codes
    let createPath = ROUTES_CONST.REQUEST.Create,
      pc = this.generalOfferTypeService.facetItemService.programCodeSelected;

    if (pc !== CONSTANTS.SC) {
      createPath = `${pc.toLocaleLowerCase()}-${ROUTES_CONST.REQUEST.Create}`;
    }
    return createPath;
  }

  subscribeCurrentOfferReqForProcess() {
    let offerRequest;
    this.requestData$.subscribe((obj) => {
      if (!obj) {
        return false;
      }
      offerRequest = Object.assign({}, obj);
    });
    if (!offerRequest) {
      return null;
    }
    return {
      id: offerRequest.info.id,
      lastUpdatedTs: offerRequest.lastUpdatedTs,
      trackType: "",
    };
  }
  subscribeCurrentEditingUser() {
    let currentEditUserobj;
    this.requestEditUpdateData$.subscribe((obj) => {
      if (!obj) {
        return false;
      }
      currentEditUserobj = Object.assign({}, obj);
    });
    if (!currentEditUserobj) {
      return null;
    }
    if (currentEditUserobj.digitalEditStatus && currentEditUserobj.digitalEditStatus.editStatus === "E") {
      return currentEditUserobj.digitalEditStatus.userId;
    } else if (currentEditUserobj.nonDigitalEditStatus && currentEditUserobj.nonDigitalEditStatus.editStatus === "E") {
      return currentEditUserobj.nonDigitalEditStatus.userId;
    } else {
      return null;
    }
  }
  subscribeCurrentOfferReq() {
    let offerRequest;
    this.requestData$.subscribe((obj) => {
      if (!obj) {
        return false;
      }
      offerRequest = Object.assign({}, obj);
    });
    if (!offerRequest) {
      return null;
    }
    return {
      id: offerRequest.info.id,
      lastUpdatedTs: offerRequest.lastUpdatedTs,
      createdApplicationId: offerRequest.createdApplicationId,
      createdTs: offerRequest.createdTs,
      createdUserId: offerRequest.createdUserId,
    };
  }

  getCurrentEditingObject() {
    let offerRequest;
    this.requestData$.subscribe((obj) => {
      if (!obj) {
        return false;
      }
      offerRequest = Object.assign({}, obj);
    });
    if (!offerRequest) {
      return null;
    }
    return {
      digitalEditStatus: offerRequest.info.digitalEditStatus,
      nonDigitalEditStatus: offerRequest.info.nonDigitalEditStatus,
    };
  }
  setJustificationSection(obj) {
    this.createdTs = obj.createdTs || Date.now();
  
    // Explicitly specify the format when parsing
    this.offerEffectiveStartDate = moment(obj.offerEffectiveStartDate, "MM/DD/YYYY", true).format("MM/DD/YYYY");
  
    if (this.offerEffectiveStartDate && this.createdTs) {
      this.formattedCreatedDate = obj.createdTs
        ? moment.unix(this.createdTs).utc(true).format("MM/DD/YYYY")
        : moment(this.createdTs).format("MM/DD/YYYY"); // Fix: Use moment() for consistency
  
      this.differenceDate =
        this.offerEffectiveStartDate && this.formattedCreatedDate
          ? moment.duration(moment(this.offerEffectiveStartDate, "MM/DD/YYYY").diff(moment(this.formattedCreatedDate, "MM/DD/YYYY"))).asDays()
          : null;
  
      if ((this.differenceDate && this.differenceDate <= 7) || this.differenceDate == 0) {
        this.isJustificationBoolean.next(true);
      } else {
        this.isJustificationBoolean.next(false);
      }
    }
  }  
  updateReqDataKeys(obj) {
    this.reqId = obj.id;
    this.lastUpdatedTs = obj.lastUpdatedTs;
    this.createdApplicationId = obj.createdApplicationId;
    this.createdTs = obj.createdTs;
    this.createdUserId = obj.createdUserId;
    this.createdApplicationId$.next(obj.createdApplicationId);
  }

  formatOffersData(deliveryChannel = null) {
    let generalInformationForm = this.generalOfferTypeService.generalOfferTypeForm.value.generalInformationForm,
      offerRequestOffers = this.generalOfferTypeService.generalOfferTypeForm.value.offerRequestOffers,
      storeGroupVersion,
      selectedOfferType = this.generalOfferTypeService.getKeyByValue(null, generalInformationForm.type);
    
    if (selectedOfferType === "WOD_OR_POD" || selectedOfferType === "REWARDS_FLAT" || selectedOfferType === "DEPARTMENT") {
      this.wodOrPodOfferRequestOffer({ offerRequestOffers, chargebackDepartment: generalInformationForm.chargebackDepartment });
    }

    if (selectedOfferType === "INSTANT_WIN") {
      offerRequestOffers[0].storeGroupVersion.productGroupVersions = [];
    } else {
      offerRequestOffers.forEach((offersElem) => {
        storeGroupVersion = offersElem.storeGroupVersion;        
        this.setPodDetailsData(storeGroupVersion);
        if(this.isVersionsForItemBasketEnabled() 
          && selectedOfferType === "ITEM_PLUS_BASKET"
          && ["SPD"].includes(this.generalOfferTypeService.facetItemService.programCodeSelected)
          ){
            if(offersElem.storeGroupVersion.productGroupVersions?.length == 1)
            {
              //We are getting either ITEM_DISCOUNT or WOD_OR_POD 
              //For Item Basket Version Processing Change the Offer type to ITEM_DISCOUNT or WOD_OR_POD
              let discountType = offersElem.storeGroupVersion.productGroupVersions?.at(0).discountVersion?.discounts?.at(0).discountType;
              offersElem.storeGroupVersion.offerPrototype = discountType === 'ITEM_LEVEL' ? "ITEM_DISCOUNT" : "WOD_OR_POD";
            }
            //Handling this case in this.setIncludeProductGroupName Method
            // else if(offersElem.storeGroupVersion.productGroupVersions?.length == 2 && storeGroupVersion.displayOrder > 1)
            // {
            //   //We are getting both ITEM_DISCOUNT and WOD_OR_POD 
            //   //For Item Basket Version Processing Set discountVersion.discounts to the primary version's discountVersion.discounts
            //   
            // }            
        }
        else
          storeGroupVersion.offerPrototype = selectedOfferType;

        const dc = deliveryChannel || this.requestForm?.controls?.offerReqGroup?.value?.deliveryChannel;
        if(selectedOfferType === "REWARDS_FLAT" 
          && (dc === "BA" || dc === "BAC" )
          && storeGroupVersion.productGroupVersions 
          && storeGroupVersion.productGroupVersions[0].productGroup.quantityUnitType
          && [CONSTANTS.SC,CONSTANTS.SPD].includes(this.generalOfferTypeService.facetItemService.programCodeSelected))
        {
          //it should be for both SPD and SC and when its is a BA type and for offer type as rewards flat
          storeGroupVersion.productGroupVersions[0].productGroup.quantityUnitType = null; 
        }
     
        if (selectedOfferType !== "INSTANT_WIN") {
          offersElem.storeGroupVersion.instantWin = null;
        }
        if (selectedOfferType === "CUSTOM") {
          storeGroupVersion.productGroupVersions = null;
        }
        this.setIncludeProductGroupName({
          storeGroupVersion,
          selectedOfferType,
        });
        
        if (dc === "IS" || dc === "PO") {
          storeGroupVersion.storeGroup.podStoreGroups = null;
        }
        if (storeGroupVersion.productGroupVersions && storeGroupVersion.productGroupVersions?.length > 0)
          storeGroupVersion.productGroupVersions[0].isGiftCard = generalInformationForm.isGiftCard;
        if (
          selectedOfferType === "REWARDS_ACCUMULATION" &&
          storeGroupVersion.productGroupVersions &&
          storeGroupVersion.productGroupVersions[0].productGroup.minPurchaseAmount
        ) {
          storeGroupVersion.productGroupVersions[0].productGroup.minPurchaseAmount =
            +storeGroupVersion.productGroupVersions[0].productGroup.minPurchaseAmount;
        } else {
          if (storeGroupVersion.productGroupVersions && storeGroupVersion?.productGroupVersions[0]?.productGroup) storeGroupVersion.productGroupVersions[0].productGroup.minPurchaseAmount = 0;
        }
      });
    }
    return offerRequestOffers;
  }
  setPodDetailsData(sgv) {
    if (sgv?.podDetails) {
      let displayEndDate = sgv.podDetails.displayEndDate;
      let displayStartDate = sgv.podDetails.displayStartDate;
      let endDateType = typeof displayEndDate;
      let startDateType = typeof displayStartDate;

      if (endDateType === "string") {
        if (displayEndDate.indexOf("+") == -1) {
          this.nonUtcCallForEndDate(sgv, displayEndDate);
        }
      } else {
        this.nonUtcCallForEndDate(sgv, displayEndDate);
      }
      if (startDateType === "string") {
        if (displayStartDate.indexOf("+") == -1) {
          this.nonUtcCallForStartDate(sgv, displayStartDate);
        }
      } else {
        this.nonUtcCallForStartDate(sgv, displayStartDate);
      }
    }
  }
  nonUtcCallForEndDate(sgv, displayEndDate) {
    sgv.podDetails.displayEndDate = dateInOriginalFormat({
      date: displayEndDate,
      isStartDate: false,
    });
  }
  nonUtcCallForStartDate(sgv, displayStartDate) {
    sgv.podDetails.displayStartDate = dateInOriginalFormat({
      date: displayStartDate,
      isStartDate: true,
    });
  }
  wodOrPodOfferRequestOffer(obj) {
    const { offerRequestOffers, chargebackDepartment } = obj;
    offerRequestOffers.forEach((offerRequestOffer) => {
      let {
        storeGroupVersion: { productGroupVersions },
      } = offerRequestOffer;
      let {
        productGroup: productGroup0,
        discountVersion: { discounts: discounts0 },
      } = productGroupVersions[0];

      let level = 0;
      productGroupVersions.forEach((productGroupVersion, index) => {
        const {
          productGroup,
          discountVersion: { discounts },
        } = productGroupVersion;

        productGroupVersion.discountVersion.discounts[0].chargebackDepartment = chargebackDepartment;

        const discounTier = discounts[0].tiers[0],
          productTier = productGroup?.tiers[0];

        if (index !== 0) {
          level = level + 1;
          discounTier.level = level;
          productTier.level = level;
          if(productGroup0) {
            productGroup0.tiers[index] = productTier;
          }

          discounts0[0].tiers[index] = discounTier;
        } else {
          discounTier.level = 1;
          if(productTier) {
            productTier.level = 1;
          }

          level = discounTier.level;
        }

        discounts0[0].displayOrder = 1;
      });

      const productGroupVer = productGroupVersions[0];
      productGroupVer.displayOrder = 1;
      offerRequestOffer.storeGroupVersion.productGroupVersions = [productGroupVer];
    });
  }
  /**
   *
   * @param pgName
   * @param pgVersion
   * @param data
   *
   * For Item + Basket, for the product group having Any Product, need to set anyProduct Flag as true
   */
  handleItemPlusBasket(pgName, pgVersion, data) {
    if (data.selectedOfferType === "ITEM_PLUS_BASKET" && pgName === "Any Product") {
      pgVersion.anyProduct = true;
    }
  }
  setIncludeProductGroupName(data) {
    // For few offertypes, benefit PG needs to be same as buy PG.
    const { storeGroupVersion } = data,
      arr = ["BUYX_GETX", "ITEM_DISCOUNT", "MEAL_DEAL", "MUST_BUY", "WOD_OR_POD", "STORE_CLOSURE", "ITEM_PLUS_BASKET","DEPARTMENT"];
    if (!arr.includes(data.selectedOfferType)) {
      return false;
    }

    if(this.isVersionsForItemBasketEnabled() && data.selectedOfferType == "ITEM_PLUS_BASKET" && storeGroupVersion.displayOrder > 1)
    {
      // TO DO: BE is expecting includeProductGroupName & includeProductGroupId same as primary offer for Item + Basket, handling it from UI
      let includedPgName, includeProductGroupId;
      storeGroupVersion.productGroupVersions.forEach((pgVersion) => {        
        includedPgName = pgVersion.productGroup.name;
        includeProductGroupId = pgVersion.productGroup.id;
        this.handleItemPlusBasket(includedPgName, pgVersion, data);
        if(storeGroupVersion.productGroupVersions.length == 1)
        {
          pgVersion.discountVersion.discounts.forEach((discount) => {
            discount.includeProductGroupName = discount.discountType === "BASKET_LEVEL" ? "Any Product" : includedPgName;
            if (discount.discountType === "BASKET_LEVEL") {
              includeProductGroupId = this.getAnyProductId();
            }
            discount.includeProductGroupId = includeProductGroupId;
            //discount.id = includeProductGroupId && includeProductGroupId.id;
          });

        }
        else{
          pgVersion.discountVersion.discounts.forEach((discount) => {
            discount.includeProductGroupName = data.selectedOfferType === "WOD_OR_POD" ? "Any Product" : includedPgName;
            if (data.selectedOfferType === "WOD_OR_POD") {
              includeProductGroupId = this.getAnyProductId();
            }
            discount.includeProductGroupId = includeProductGroupId;
            //discount.id = includeProductGroupId && includeProductGroupId.id;
          });
        }
      });
    }
    else{
      // TO DO: BE is expecting includeProductGroupName for all the offers, handling it from UI
      let includedPgName, includeProductGroupId;
      storeGroupVersion.productGroupVersions.forEach((pgVersion) => {
        includedPgName = pgVersion.productGroup.name;
        includeProductGroupId = pgVersion.productGroup.id;
        this.handleItemPlusBasket(includedPgName, pgVersion, data);
        pgVersion.discountVersion.discounts.forEach((discount) => {
          discount.includeProductGroupName = data.selectedOfferType === "WOD_OR_POD" ? "Any Product" : includedPgName;
          if (data.selectedOfferType === "WOD_OR_POD") {
            includeProductGroupId = this.getAnyProductId();
          }
          discount.includeProductGroupId = includeProductGroupId;
          //discount.id = includeProductGroupId && includeProductGroupId.id;
        });
      });
    }
  }
  getAnyProductId() {
    const productDefault = this._initialDataService.getAppDataName("defaultGroups");
    const productGroups = productDefault.productGroups;
    return Object.keys(productGroups).map((ele) => {
      if (productGroups[ele] === "Any Product") {
        return { name: productGroups[ele], id: ele };
      }
    })[0];
  }
  getValueByKey(object, key) {
    if (!object) {
      object = this.appData?.offerType;
    }
    return object[key];
  }
  getDiscountByKey(object, key) {
    if (!object) {
      object = this.appData.amountTypes;
    }
    return object[key];
  }

  getOfferStatusClass(offerStatus) {
    switch (offerStatus) {
      case "Draft": {
        return "draft-status-background m-0";
      }
      case "Deployed": {
        return "deploy-status-background m-0";
      }
      case "Preview": {
        return "preview-status-background m-0";
      }
      case "Published": {
        return "publish-status-background m-0";
      }
      case "Expired": {
        return "expired-status-background m-0";
      }
      case "Canceled": {
        return "cancelled-status-background m-0";
      }
      case "Publishing": {
        return "cancelled-status-background m-0";
      }
      case "Failed Deployment": {
        return "cancelled-status-background m-0";
      }
      case "Failed Publishing": {
        return "cancelled-status-background m-0";
      }
      default:
        return "align-status";
    }
  }
  prependZero(element) {
    if (element !== null && typeof element !== "undefined" && element.toString().length < 2) {
      element = `0${element}`;
    }
    return element;
  }
  mapFormDataToReqObj() {
    // Get the data from form and for the mapping for saving
    let search = this.requestForm.value.offerBuilderGroup;
    let digitalUser = this.digitalUserDetails(search.digital);
    let nonDigitalUser = this.nonDigitalUserDetails(search.nonDigital);
    let {
      deliveryChannel = null,
      department = null,
      programCode = null,
      customerSegment = null,
      group = null,
      groupDivision = null,
      brandAndSize = null,
      offerEffectiveStartDate = null,
      offerEffectiveEndDate = null,
      usageLimitTypePerUser = null,
      customPeriod = null,
      customType = null,
      customUsage = null,
      usageLimitPerUser = null,

      pluTriggerBarcode = null,
      adType,
      digitalStatus,
      nonDigitalStatus,
      editStatus,
      digitalEditStatus,
      nonDigitalEditStatus,
      numOfTiers = 1,
      fulfillmentChannel,
      ecommPromoType = null, //ecommFields
      ecommPromoCode  = null, //ecommFields
      cpg,
      order  = null, //ecommFields
      notCombinableWith = null,
      orderCount= null,
      behavioralAction = null,
      isAutoApplyPromoCode = false,
      validWithOtherOffer = false,
      firstTimeCustomerOnly= false,
      isDynamicOffer = null,//DynamicOfferFields
      daysToRedeem, //DynamicOfferFields
      initialSubscriptionOffer = false, //InitialSubscriptionOffer
      isRedeemableInSameTransaction = false,
      behavioralCondition = {}
    } = this.requestForm.value.offerReqGroup;

    let {
      nopaNumbers = null,
      billingOptions = null,
      isBilled = false,
      nopaStartDate = null,
      nopaEndDate = null,
    } = this.requestForm.value.nopaGroup;
    const {
      noOfTransactions = null,
      minimumSpend = null,
      minimumSpendUom = null
    } = behavioralCondition || {}; 
    // ACIP-382481: StartDate may be disabled so need to get the raw value from the form
    offerEffectiveStartDate = this.requestForm.getRawValue().offerReqGroup.offerEffectiveStartDate;
    const { desc = null, offerFlag = null } = this.requestForm.value.additionalDescriptionGroup;
    const { justification = null } = this.requestForm.value.justificationGroup;
    let day = {};
    if (this.requestForm.value.offerRuleDayGroup) {
      day = this.requestForm.value.offerRuleDayGroup;
    }
    let time = {};

    if (
      this.requestForm.value.offerRuleTimeGroup &&
      this.requestForm.value.offerRuleTimeGroup.start != null &&
      this.requestForm.value.offerRuleTimeGroup.end != null
    ) {
      // ACIP-44095: changing as per new sonar guidelines 
      let formValue = this.requestForm.value.offerRuleTimeGroup;
      let timestart = `2023-01-01 ${formValue.start}`;
      let timeend = `2023-01-01 ${formValue.end}`;
      if (formValue.start) {
        formValue.startHr = moment(timestart).format('hh');
        formValue.startMin = moment(timestart).format('mm');
        formValue.startPeriod = moment(timestart).format('A');
      }

      if (formValue.end) {
        formValue.endHr = moment(timeend).format('hh');
        formValue.endMin = moment(timeend).format('mm');
        formValue.endPeriod = moment(timeend).format('A');
      }
      // prepending zeros for time
      this.requestForm.value.offerRuleTimeGroup.start = moment(timestart).format('hh:mm A');
      this.requestForm.value.offerRuleTimeGroup.end = moment(timeend).format('hh:mm A');;
      time = {
        start: this.requestForm.value.offerRuleTimeGroup.start,
        end: this.requestForm.value.offerRuleTimeGroup.end,
      };
    }
    let offerRequestOffers = this.parseOfferRequestOffers(deliveryChannel);

    const obj = this.subscribeCurrentOfferReq() || {
        id: null,
        lastUpdatedTs: null,
        createdApplicationId: CONSTANTS.OMS,
        createdTs: null,
        createdUserId: null,
      },
      { id = null, lastUpdatedTs = null, createdApplicationId = CONSTANTS.OMS, createdTs = null, createdUserId = null } = obj;

    if (offerEffectiveStartDate) {
      offerEffectiveStartDate = dateInOriginalFormat({
        date: offerEffectiveStartDate,
        isStartDate: true,
      });
    }
    if (offerEffectiveEndDate) {
      offerEffectiveEndDate = dateInOriginalFormat({
        date: offerEffectiveEndDate,
        isStartDate: false,
      });
    }

    if (nopaStartDate) {
      nopaStartDate = dateInOriginalFormat({ date: nopaStartDate, isStartDate: true });
    }

    if (nopaEndDate) {
      nopaEndDate = dateInOriginalFormat({ date: nopaEndDate, isStartDate: false });
    }
    let nopaNums;
    if (nopaNumbers) {
      nopaNums = nopaNumbers.split(",").reduce((output, ele, index) => {
        if (index === 0) {
          output = `${ele.trim()}`;
        } else {
          output = `${output},${ele.trim()}`;
        }
        return output;
      }, "");
    }

    let nopaNumbersUpdated = nopaNums ? nopaNums.replace(/[',\n ']/g, ",") : [];

    nopaNumbersUpdated.length > 0 ? (nopaNumbersUpdated = nopaNums.split(",").filter((item) => item !== "")) : (nopaNumbersUpdated = []);

    if (groupDivision) {
      const groupIndex = group && group.indexOf(":") > -1 ? group.split(": ")[1] : group;
      const groupDivisionIndex = groupDivision.indexOf(":") > -1 ? groupDivision.split(": ")[1] : groupDivision;
      const groupDivisionArray = this.appData.offerRequestGroups[groupIndex].groupDivisions;
      groupDivision = typeof groupDivisionArray !== "undefined" ? groupDivisionArray[groupDivisionIndex]["code"] : null;
    }

    if (group) {
      const groupIndex = group && group.indexOf(":") > -1 ? group.split(": ")[1] : group;
      group = this.appData.offerRequestGroups[groupIndex]["code"];
    }

    const generalInformationForm = this.generalOfferTypeService.generalOfferTypeForm.value.generalInformationForm;
    const offerRequestType = this.generalOfferTypeService.getKeyByValue(null, generalInformationForm.type);
    if (offerRequestType === "WOD_OR_POD" || offerRequestType === "REWARDS_FLAT" || offerRequestType === "DEPARTMENT") {
      numOfTiers = generalInformationForm.tiers;
    }
    if(offerRequestType === "CONTINUITY" && [CONSTANTS.SPD,CONSTANTS.SC].includes(this.generalOfferTypeService.facetItemService.programCodeSelected))
    {
      isRedeemableInSameTransaction = generalInformationForm.isRedeemableInSameTransaction;
    }

    let _department = CONSTANTS.DEFAULT_DEPARTMENT;
    if(!(this.appData.departments as Array<string>).includes(department))
    {
       department = _department;
    }
    if(!id){
      digitalEditStatus = null;
      nonDigitalEditStatus = null;
    }
    this.requestData = {
      lastUpdatedTs,
      createdApplicationId,
      createdTs,
      createdUserId,
      info: {
        id,
        deliveryChannel,
        group,
        groupDivision,
        adType,
        offerRequestType, // TO DO :: "ITEM_DISCOUNT"
        brandAndSize,
        nopaNumbers: nopaNumbersUpdated,
        nopaStartDate,
        nopaEndDate,
        billingOptions,
        isBilled,
        pluTriggerBarcode,
        desc,
        offerFlag: offerFlag?.length ? offerFlag : null,
        imageId: null,
        programCode,
        attachments: null,
        digitalUser: digitalUser || {},
        nonDigitalUser: nonDigitalUser || {},
        digitalStatus,
        nonDigitalStatus,
        editStatus,
        digitalEditStatus,
        nonDigitalEditStatus,
        numOfTiers,
        justification,
        fulfillmentChannel,
        ecommPromoType , //ecommFields
        ecommPromoCode, //ecommFields
        cpg,
        order, //ecommFields
        notCombinableWith: notCombinableWith?.length ? notCombinableWith.split(',') : [],
        behavioralAction,
        orderCount: orderCount?.length ? [...new Set(orderCount?.split(','))]?.join() : orderCount,
        isAutoApplyPromoCode,
        firstTimeCustomerOnly,
        validWithOtherOffer,
        isDynamicOffer,
        daysToRedeem,
        initialSubscriptionOffer,
        isRedeemableInSameTransaction,
        behavioralCondition: {
          noOfTransactions,
          minimumSpend,
          minimumSpendUom
        }
      },
      rules: {
        startDate: {
          displayEffectiveStartDate: null,
          offerEffectiveStartDate,
          offerTestEffectiveStartDate: null,
        },
        endDate: {
          displayEffectiveEndDate: null,
          offerEffectiveEndDate,
          offerTestEffectiveEndDate: null,
        },
        department,
        customerSegment,
        usageLimitTypePerUser,
        usageLimitPerUser,
        customPeriod,
        customType,
        customUsage,
        qualificationAndBenefit: {
          day,
          time,
          offerRequestOffers,
        },
      },
    };
    const _deliveryChannel =this.requestData?.info?.deliveryChannel;
    if(_deliveryChannel !== CONSTANTS.BEHAVIORAL_CONTINUTY_CODE){
      delete this.requestData?.info?.behavioralCondition;
    }
  }
  parseOfferRequestOffers(deliverChannel = "") {
    let offerRequestOffers = this.formatOffersData(deliverChannel);
    // Display order changes on save
    const isNewOR = offerRequestOffers.filter((ele) => ele.id === null).length === offerRequestOffers.length;
    offerRequestOffers.forEach((item, index) => {
      if (!item.id) {
        this.setDisplayOrderForVersion(item, index, isNewOR);
      }
    });
    this.setGroupValues(offerRequestOffers);
    return offerRequestOffers;
  }
  setDetectChanges(offerRequestOffers) {
    const offerRequestData = JSON.parse(this.generalOfferTypeService.cloneOfferRequestData);
    const offerType = this.generalOfferTypeService.generalOfferTypeForm.get("generalInformationForm").value["type"];
    const selectedChannel = this.requestForm.get("offerReqGroup").value["deliveryChannel"];
    const tagOfferTypes = ["Item Discount", "Buy X Get X", "Buy X Get Y", "Must Buy"];
    if (!this.allOffersAlongWithOfferRequestUpdate) {
      this.allOffersAlongWithOfferRequestUpdate = false;
    }
    offerRequestOffers.forEach((savePayload) => {
      if (savePayload.id === null) {
        this.allOffersAlongWithOfferRequestUpdate = false;
      }
      const versionData = offerRequestData.filter((payload) => payload.id === savePayload.id);
      if (versionData.length) {
        const {
          storeGroupVersion: {
            productGroupVersions: requestProductGroupVersions,
            storeGroup: requestStoreGroup,
            instantWin: requestInstantWin,
            storeTag: requestStoreTag,
          },
        } = versionData[0];
        const {
          storeGroupVersion: {
            productGroupVersions: saveProductGroupVersions,
            storeGroup: saveStoreGroup,
            instantWin: saveInstantWin,
            storeTag: saveStoreTag,
          },
        } = savePayload;

        this.detectStoreGroupChanges(requestStoreGroup, saveStoreGroup);
        if ((selectedChannel === "CC" || selectedChannel === "DO") && tagOfferTypes.includes(offerType)) {
          this.detectStoreTagChanges(requestStoreTag, saveStoreTag);
        }
        if (offerType === "Alaska Airmiles") {
          saveProductGroupVersions &&
            saveProductGroupVersions.forEach((ele) => {
              if (ele && ele.discountVersion) {
                ele.discountVersion.discounts = [];
              }
            });
        }
        if (offerType !== "Custom") {
          if (offerType !== "Enterprise Instant Win") {
            this.detectProductGroupChanges(requestProductGroupVersions, saveProductGroupVersions);
            this.detectDiscountVersionChanges(requestProductGroupVersions, saveProductGroupVersions);
          } else {
            this.detectInstantWinChanges(requestInstantWin, saveInstantWin);
          }
        }
      }
    });
  }

  detectInstantWinChanges(requestInstantWin, saveInstantWin) {
    const detectChange = this.detectObjectComparision(requestInstantWin, saveInstantWin);
    saveInstantWin.updateInstantWin = false;
    if (detectChange) {
      saveInstantWin.updateInstantWin = true;
      this.allOffersAlongWithOfferRequestUpdate = false;
    }
  }

  detectStoreTagChanges(requestStoreTag, saveStoreTag) {
    saveStoreTag.updateStoreTag = false;
    const detectChange = requestStoreTag && saveStoreTag && this.detectObjectComparision(requestStoreTag, saveStoreTag);
    if (detectChange) {
      saveStoreTag.updateStoreTag = true;
      this.allOffersAlongWithOfferRequestUpdate = false;
    }
  }

  detectStoreGroupChanges(requestStoreGroup, saveStoreGroup) {
    const digitalRedemptionStoreGroup = this.compareStoreGroups(
      requestStoreGroup.digitalRedemptionStoreGroupIds || [],
      saveStoreGroup.digitalRedemptionStoreGroupIds || []
    );
    const podStoreGroup = this.compareStoreGroups(requestStoreGroup.podStoreGroupIds || [], saveStoreGroup.podStoreGroupIds || []);
    const nonDigitalRedemptionStoreGroup = this.compareStoreGroups(
      requestStoreGroup.nonDigitalRedemptionStoreGroupIds || [],
      saveStoreGroup.nonDigitalRedemptionStoreGroupIds || []
    );
    saveStoreGroup.updateDigitalStoreGroup = false;
    saveStoreGroup.updateNonDigitalStoreGroup = false;
    saveStoreGroup.updatePODStoreGroup = false;
    if (digitalRedemptionStoreGroup) {
      saveStoreGroup.updateDigitalStoreGroup = true;
    }
    if (podStoreGroup) {
      saveStoreGroup.updatePODStoreGroup = true;
    }
    if (nonDigitalRedemptionStoreGroup) {
      saveStoreGroup.updateNonDigitalStoreGroup = true;
    }
    if (digitalRedemptionStoreGroup || podStoreGroup || nonDigitalRedemptionStoreGroup) {
      this.allOffersAlongWithOfferRequestUpdate = false;
    }
  }

  /* To detect changes in the Buy Section */
  detectProductGroupChanges(requestProductGroupVersions, saveProductGroupVersions) {
    saveProductGroupVersions.forEach((productGroupVersion) => {
      if (productGroupVersion.id === null) {
        this.allOffersAlongWithOfferRequestUpdate = false;
      }
      const productGV = requestProductGroupVersions.filter((payload) => payload.id === productGroupVersion.id);
      if (productGV.length) {
        const { productGroup: requestProductGroup } = productGV[0];
        const { productGroup: saveProductGroup } = productGroupVersion;
        saveProductGroup.updateProductGroup = false;
        const detectChange = this.detectObjectComparision(requestProductGroup, saveProductGroup);
        if (detectChange) {
          saveProductGroup.updateProductGroup = true;
          this.allOffersAlongWithOfferRequestUpdate = false;
        }
      }
    });
  }
  /* To detect changes in Get Section */
  detectDiscountVersionChanges(requestProductGroupVersions, saveProductGroupVersions) {
    saveProductGroupVersions.forEach((productGroupVersion) => {
      const productGV = requestProductGroupVersions.filter((payload) => payload.id === productGroupVersion.id);
      if (productGV.length) {
        const {
          discountVersion: { discounts: requestDiscounts, airMiles: requestAirMiles },
        } = productGV[0];
        const {
          discountVersion: { discounts: saveDiscounts, airMiles: saveAirmiles },
        } = productGroupVersion;
        if (saveDiscounts && saveDiscounts.length) {
          saveDiscounts.forEach((saveDiscount) => {
            saveDiscount.updateProductDiscount = false;
            const requestDiscount = requestDiscounts.filter((payload) => payload.id === saveDiscount.id);
            const detectChange = requestDiscount.length && this.detectObjectComparision(requestDiscount[0], saveDiscount);
            if (detectChange) {
              saveDiscount.updateProductDiscount = true;
              this.allOffersAlongWithOfferRequestUpdate = false;
            }
          });
        } else if (saveAirmiles && saveAirmiles.length) {
          saveAirmiles.forEach((saveAirmile) => {
            const requestAirmile = requestAirMiles.filter((payload) => payload.id === saveAirmile.id);
            const detectChange = requestAirmile.length && this.detectObjectComparision(requestAirmile[0], saveAirmile);
            saveAirmile.updateAirMiles = false;
            if (detectChange) {
              saveAirmile.updateAirMiles = true;
              this.allOffersAlongWithOfferRequestUpdate = false;
            }
          });
        }
      }
    });
  }
  comparer(requestTier, current) {
    const tier = requestTier.filter((element) => element.level === current.level)[0];
    return (
      tier &&
      Object.keys(current).filter((element) => {
        return (tier[element] || current[element]) && tier[element] != current[element];
      }).length
    );
  }

  detectTierComparision(requestTier, saveObjTier, saveObj) {
    if (requestTier.length !== saveObjTier.length) {
      if (saveObj.hasOwnProperty("updateProductDiscount")) {
        saveObj.updateProductDiscount = true;
      } else if (saveObj.hasOwnProperty("updateProductGroup")) {
        saveObj.updateProductGroup = true;
      }
    }
    let detectChanges = saveObjTier.filter((element) => this.comparer(requestTier, element));
    return detectChanges.length;
  }

  isExpiredStatusAndNotCancelled({status, endDate}) {
    return status !== CONSTANTS.CANCELLED && status !== CONSTANTS.NA && this.isDateExpired(endDate)
  }

  isExpiredStatus({ status, endDate }) {
    if (status === CONSTANTS.COMPLETED_STATUS_OR && this.isDateExpired(endDate)) {
      return true;
    }
  }

  isDateExpired(endDate) {
    //checks if the end dt is expired. Used in setting the status as expired

    if (!endDate) {
      return null;
    }

    const todayDate = moment(new Date(), "YYYY-MM-DD");
    const endDt = moment(endDate, "YYYY-MM-DD");
    const days = endDt.diff(todayDate, "days");
    if (days < 0) {
      return true;
    }
    return null;
  }

  detectObjectComparision(requestObj, saveObj) {
    const changes = Object.keys(saveObj).filter((element) => {
      return toString.call(saveObj[element]) === "[object Array]"
        ? this.detectTierComparision(requestObj[element], saveObj[element], saveObj)
        : (requestObj[element] || saveObj[element]) && requestObj[element] != saveObj[element];
    });
    return changes.length;
  }

  setGroupValues(offerRequestOffers) {
    const storeIds = ["digitalRedemptionStoreGroupIds", "nonDigitalRedemptionStoreGroupIds", "podStoreGroupIds"],
      productIds = ["id"],
      discountIds = ["includeProductGroupId"];
    offerRequestOffers.forEach((offerRequest) => {
      const { storeGroup, productGroupVersions } = offerRequest.storeGroupVersion;
      const storeKeys = Object.keys(storeGroup);
      storeKeys.forEach((store) => {
        if (storeIds.includes(store) && storeGroup[store]) {
          storeGroup[store] = storeGroup[store].map((element) => (toString.call(element) === "[object Object]" ? element.id : element));
        }
      });
      productGroupVersions &&
        productGroupVersions.forEach((productGroupVersion) => {
          const {
            discountVersion: { discounts },
            productGroup,
          } = productGroupVersion;
          const productGroupKeys = productGroup && Object.keys(productGroup);
          productGroupKeys?.forEach((productGroupKey) => {
            if (productIds.includes(productGroupKey) && productGroup[productGroupKey]) {
              const prodKey = productGroup[productGroupKey];
              if (prodKey && prodKey["id"]) {
                productGroup[productGroupKey] = prodKey["id"];
              }
            }
          });
          discounts &&
            discounts.forEach((discount) => {
              const discountKeys = Object.keys(discount);
              discountKeys.forEach((discountKey) => {
                if (discountIds.includes(discountKey) && discount[discountKey]) {
                  const disKey = discount[discountKey];
                  if (disKey && disKey["id"]) {
                    discount[discountKey] = +disKey["id"];
                  }
                }
              });
            });
        });
    });
  }
  setDisplayOrderForVersion(element, index, isNewOR) {
    let storeGroupVer = nullCheckProperty(element, "storeGroupVersion");
    if (isNewOR) {
      storeGroupVer.displayOrder = index + 1;
    }
    let prodVer = nullCheckProperty(storeGroupVer, "productGroupVersions");
    prodVer &&
      prodVer.forEach((elem, indx) => {
        elem.displayOrder = indx + 1;
        let discounts = nullCheckProperty(elem, "discountVersion.discounts");
        discounts &&
          discounts.forEach((val, ind) => {
            val.displayOrder = ind + 1;
          });
      });
  }

  setReqServiceVariables(obj) {
    // Values passed from the offer request tabs would be set here.
    this.compRoute = obj.route;
    this.compRouter = obj.router;
  }

  resetOnDestroy() {
    if (this.generalOfferTypeService.isReqSubmitAttempted$) {
      this.generalOfferTypeService.isReqSubmitAttempted$.next(false);
    }
    this.isReqSubmitAttempted$.next(false);
    this.isDraftSaveAttempted.next(false);
    this.generalOfferTypeService.totalStoresCount = 0;
    this.isReqSubmitted = null;
    this.uploadedFilesList = [];
    this.requestData$.next(null);
    this.requestData = {};
    this.attachedFilesList = [];
    this.isJustificationBoolean.next(false);
    this.isSavedFromNavigationOverlay = false;
    this.digitalStatus = this.nonDigitalStatus = null;
  }

  isMinStateSubmit() {
    let state = false,
      preSubmitStatusesArr = ["I", "NA"];

    if (this.digitalStatus) {
      if (preSubmitStatusesArr.indexOf(this.digitalStatus) > -1) {
        // already set
      } else {
        state = true;
      }
    }

    if (this.nonDigitalStatus) {
      if (preSubmitStatusesArr.indexOf(this.nonDigitalStatus) > -1) {
        // don't reset if set in previous
      } else {
        state = true;
      }
    }

    return state;
  }

  isMinStateProcessing() {
    let state = false;
    if (this.digitalStatus) {
      if (this.digitalStatus === "P" || this.digitalStatus === "C" || this.digitalStatus === "D") {
        state = true;
      }
    }

    if (this.nonDigitalStatus) {
      if (this.nonDigitalStatus === "P" || this.nonDigitalStatus === "C" || this.nonDigitalStatus === "D") {
        state = true;
      }
    }

    return state;
  }
  checkFulfillmentChannelEnabled(pCode,deliveryChannel){
    return ["SC","SPD"].includes(pCode) && ["DO","IS","EC","DAC", "BA", "BAC"].includes(deliveryChannel);
  }
  canEditOfferStartDate(digitalStatus,nonDigitalStatus, rules) {
      return this._searchOfferRequestService.canEditOfferStartDate(digitalStatus,nonDigitalStatus, rules);
  }  
}
